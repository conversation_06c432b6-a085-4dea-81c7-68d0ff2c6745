#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试前端错误处理
"""

import requests
import io
from PIL import Image

def test_frontend_error_handling():
    """测试前端错误处理"""
    print('🧪 测试前端错误处理...')
    
    # 创建一个没有人脸的测试图像
    img = Image.new('RGB', (300, 400), color=(255, 0, 0))  # 纯红色
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    try:
        # 模拟前端请求
        files = {'photo': ('test.jpg', img_bytes, 'image/jpeg')}
        data = {
            'size': 'one_inch',
            'background': 'blue'
        }
        
        print('📤 发送请求到SuperSpider证件照API...')
        response = requests.post('http://127.0.0.1:5000/api/idphoto/create', 
                               files=files, data=data, timeout=30)
        
        print(f'📥 响应状态码: {response.status_code}')
        print(f'📥 响应头: {dict(response.headers)}')
        
        try:
            result = response.json()
            print(f'📥 响应内容: {result}')
            
            if response.status_code == 400:
                print('✅ 正确返回400错误状态码')
                if 'message' in result:
                    print(f'✅ 错误信息: {result["message"]}')
                else:
                    print('❌ 缺少错误信息')
            elif response.status_code == 200:
                if result.get('success'):
                    print('✅ 请求成功')
                else:
                    print(f'❌ 请求失败: {result.get("message", "未知错误")}')
            else:
                print(f'❌ 意外的状态码: {response.status_code}')
                
        except Exception as json_error:
            print(f'❌ 无法解析JSON响应: {json_error}')
            print(f'   原始响应: {response.text}')
            
    except Exception as e:
        print(f'❌ 请求异常: {e}')

def test_with_real_face():
    """测试真实人脸图像处理"""
    print('\n🎨 测试真实人脸图像处理...')
    
    # 创建一个更像人脸的测试图像
    img = Image.new('RGB', (400, 500), color=(255, 255, 255))  # 白色背景
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # 绘制一个更复杂的人脸
    # 脸部轮廓
    draw.ellipse([100, 150, 300, 350], fill=(255, 220, 177))  # 肤色
    # 眼睛
    draw.ellipse([130, 200, 150, 220], fill=(255, 255, 255))  # 左眼白
    draw.ellipse([135, 205, 145, 215], fill=(0, 0, 0))       # 左眼珠
    draw.ellipse([250, 200, 270, 220], fill=(255, 255, 255))  # 右眼白
    draw.ellipse([255, 205, 265, 215], fill=(0, 0, 0))       # 右眼珠
    # 鼻子
    draw.polygon([(200, 230), (190, 260), (210, 260)], fill=(255, 200, 160))
    # 嘴巴
    draw.ellipse([180, 280, 220, 300], fill=(255, 100, 100))
    
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    try:
        files = {'photo': ('face_test.jpg', img_bytes, 'image/jpeg')}
        data = {
            'size': 'one_inch',
            'background': 'blue'
        }
        
        print('📤 发送人脸图像到SuperSpider...')
        response = requests.post('http://127.0.0.1:5000/api/idphoto/create', 
                               files=files, data=data, timeout=60)
        
        print(f'📥 响应状态码: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print('✅ 人脸图像处理成功!')
                print(f'   尺寸信息: {result["data"].get("size_info", "未知")}')
                print(f'   背景颜色: {result["data"].get("background_color", "未知")}')
                if result["data"].get("single_photo"):
                    print(f'   单张证件照: {len(result["data"]["single_photo"])} 字符')
                if result["data"].get("layout_photo"):
                    print(f'   排版照: {len(result["data"]["layout_photo"])} 字符')
            else:
                print(f'❌ 处理失败: {result.get("message", "未知错误")}')
        else:
            result = response.json()
            print(f'❌ 请求失败: {result.get("message", "未知错误")}')
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')

if __name__ == '__main__':
    test_frontend_error_handling()
    test_with_real_face()

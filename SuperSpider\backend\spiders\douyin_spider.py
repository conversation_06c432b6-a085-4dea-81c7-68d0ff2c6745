#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
抖音爬虫模块
用于解析抖音视频链接，获取无水印视频信息
"""

import json
import logging
import re
import requests
import traceback
from functools import lru_cache
from typing import Dict, Any, Optional
from urllib.parse import quote
from requests.exceptions import ConnectionError, Timeout, RequestException
from urllib3.exceptions import NameResolutionError
from DrissionPage import ChromiumPage
from DrissionPage import ChromiumOptions

from .base_spider import BaseSpider
from ..utils.settings import DOU_YIN_COOKIE

# 创建日志记录器
logger = logging.getLogger(__name__)

hl = ChromiumOptions().headless(True)
dp = ChromiumPage(hl)


@lru_cache(maxsize=20)
def get_final_url(url, cookie=DOU_YIN_COOKIE):
    response = requests.get(
        url,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Cookie': cookie
        },
        allow_redirects=True,
        timeout=10
    )
    final_url = response.url.split('?')[0].split('#')[0]
    # print(final_url)
    return final_url


def clean_text(text):
    if not text:
        return ""
    return re.sub(r'[\r\xa0\x00-\x09\x0b-\x0c\x0e-\x1f\x7f-\xa0\u200b-\u200f\u2028-\u202f\u3000]+', '', text).strip()


class DouyinSpider(BaseSpider):
    """
    抖音爬虫类
    继承自BaseSpider，实现抖音视频解析功能
    专注于获取无水印视频链接，不进行本地下载
    """

    def __init__(self):
        """初始化抖音爬虫"""
        super().__init__("抖音爬虫")
        self.platform = "douyin"

        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'cookie': DOU_YIN_COOKIE,
        }

    def _get_user_friendly_error(self, error):
        """将技术错误转换为用户友好的错误信息"""
        error_str = str(error).lower()

        if isinstance(error, (ConnectionError, NameResolutionError)) or 'failed to resolve' in error_str:
            return "网络连接失败，请检查网络连接或稍后重试"
        elif isinstance(error, Timeout) or 'timeout' in error_str:
            return "请求超时，请检查网络连接或稍后重试"
        elif 'max retries exceeded' in error_str:
            return "网络请求失败，服务器可能暂时不可用，请稍后重试"
        elif 'ssl' in error_str or 'certificate' in error_str:
            return "安全连接失败，请检查网络设置"
        elif 'permission denied' in error_str:
            return "访问被拒绝，可能是网络限制或防火墙阻止"
        elif 'invalid url' in error_str or 'url' in error_str:
            return "视频链接格式不正确，请检查链接是否完整"
        elif 'drissionpage' in error_str or 'chrome' in error_str:
            return "浏览器驱动异常，请稍后重试"
        else:
            return "解析失败，请检查视频链接是否有效或稍后重试"

    def validate_url(self, url: str) -> bool:
        """
        验证抖音URL是否有效

        Args:
            url: 待验证的URL

        Returns:
            bool: URL是否有效
        """
        try:
            # 抖音URL格式示例:
            # https://www.douyin.com/video/7xxx
            # https://v.douyin.com/xxx
            # https://www.iesdouyin.com/share/video/xxx
            douyin_patterns = [
                r'douyin\.com',
                r'iesdouyin\.com',
                r'v\.douyin\.com'
            ]

            return any(re.search(pattern, url) for pattern in douyin_patterns)
        except Exception as e:
            logger.error(f"URL验证失败: {str(e)}")
            return False

    def extract_video_info(self, url: str) -> Dict[str, Any]:
        """
        从抖音URL提取视频信息

        Args:
            url: 抖音视频URL

        Returns:
            Dict: 包含视频信息的字典
        """
        try:
            # 获取最终重定向URL
            final_url = get_final_url(url)
            logger.info(f"获取到最终URL: {final_url}")

            # 更新请求头
            headers = self.headers.copy()
            headers.update({'referer': final_url})

            # 使用DrissionPage监听API请求
            dp.listen.start('aweme/v1/web/aweme/detail/?')
            dp.get(final_url)
            res = dp.listen.wait()
            data = res.response.body

            # 提取视频信息
            aweme_detail = data.get('aweme_detail', {})
            author_info = aweme_detail.get('author', {})
            video_info = aweme_detail.get('video', {})
            statistics = aweme_detail.get('statistics', {})

            # 清理文本
            author = clean_text(author_info.get('nickname', ''))
            title = clean_text(aweme_detail.get('desc', ''))

            # 获取无水印视频链接
            url_list = video_info.get('play_addr', {}).get('url_list', [])

            video_url = next(
                (clean_text(url) for url in url_list if "v3-web" in url),
                url_list[0] if url_list else ""
            )

            # 如果没有找到v3-web链接，尝试其他链接
            if not video_url and url_list:
                video_url = clean_text(url_list[0])

            # 获取封面图片
            cover_url = video_info.get('cover', {}).get('url_list', [])
            cover_image = cover_url[0] if cover_url else ""

            # 构建返回数据
            result = {
                'title': title,
                'author': author,
                'videoUrl': video_url,  # 直接返回无水印链接
                'coverUrl': cover_image,
                'description': title,  # 抖音的描述就是标题
                'duration': video_info.get('duration', 0) // 1000,  # 转换为秒
                'playCount': statistics.get('play_count', 0),
                'likeCount': statistics.get('digg_count', 0),
                'commentCount': statistics.get('comment_count', 0),
                'shareCount': statistics.get('share_count', 0)
            }

            logger.info(f"成功解析视频信息: {title}")
            return result

        except Exception as e:
            logger.error(f"提取视频信息失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 抛出用户友好的错误信息
            user_error = self._get_user_friendly_error(e)
            raise Exception(user_error)

    def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行抖音爬虫任务

        Args:
            task: 任务参数
                {
                    "video_url": "抖音视频URL",
                    "download": bool  # 是否下载视频（已废弃，保留兼容性）
                }

        Returns:
            Dict: 执行结果
        """
        try:
            video_url = task.get("video_url")

            if not video_url:
                return {
                    "success": False,
                    "message": "请提供视频链接",
                    "data": None
                }

            # 验证URL
            if not self.validate_url(video_url):
                return {
                    "success": False,
                    "message": "请提供有效的抖音视频链接",
                    "data": None
                }

            # 提取视频信息
            video_info = self.extract_video_info(video_url)

            if not video_info:
                return {
                    "success": False,
                    "message": "无法获取视频信息，请检查链接是否有效",
                    "data": None
                }

            return {
                "success": True,
                "message": "解析成功",
                "data": video_info
            }

        except Exception as e:
            logger.error(f"抖音爬虫执行失败: {str(e)}")
            logger.error(traceback.format_exc())

            # 返回用户友好的错误信息
            user_error = str(e) if any(keyword in str(e) for keyword in
                                     ['网络连接失败', '请求超时', '视频', '链接', '格式', '浏览器']) else self._get_user_friendly_error(e)

            return {
                "success": False,
                "message": user_error,
                "data": None
            }

    def check_status(self) -> Dict[str, Any]:
        """
        检查抖音爬虫状态

        Returns:
            Dict: 状态信息
        """
        return {
            "name": self.name,
            "platform": self.platform,
            "status": "ready",
            "version": "1.0.0"
        }

# 测试案例（已移除，使用类方法进行测试）
# if __name__ == '__main__':
#     spider = DouyinSpider()
#     result = spider.execute({
#         "video_url": "https://www.douyin.com/video/7xxx",
#         "download": False
#     })
#     print(result)

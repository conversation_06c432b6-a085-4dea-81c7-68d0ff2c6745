#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试证件照API
"""

import requests
import time
import os

def test_idphoto_api():
    """测试证件照API"""
    print("🔍 测试证件照API...")
    
    # 创建会话并登录
    session = requests.Session()
    
    # 登录
    login_data = {
        "account": "test",
        "password": "test123"
    }
    
    print("📤 登录...")
    login_response = session.post('http://127.0.0.1:5000/api/auth/login', 
                                json=login_data, timeout=10)
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return
    
    print("✅ 登录成功")
    
    # 测试证件照API
    test_image = "SuperSpider/backend/utils/HivisionIDPhotos-master/demo/images/test0.jpg"
    
    if not os.path.exists(test_image):
        print("❌ 测试图片不存在")
        return
    
    try:
        with open(test_image, 'rb') as f:
            files = {'photo': ('test0.jpg', f, 'image/jpeg')}
            data = {
                'size': '1inch',
                'background': 'blue'
            }
            
            print("📤 发送证件照请求...")
            print(f"   文件: {test_image}")
            print(f"   参数: {data}")
            
            start_time = time.time()
            
            response = session.post('http://127.0.0.1:5000/api/idphoto/create', 
                                  files=files, data=data, timeout=120)
            
            end_time = time.time()
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"⏱️ 耗时: {end_time - start_time:.2f}秒")
            print(f"📦 响应大小: {len(response.content)} 字节")
            
            # 打印响应头
            print("📋 响应头:")
            for key, value in response.headers.items():
                print(f"   {key}: {value}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print("✅ JSON解析成功")
                    print(f"   success: {result.get('success')}")
                    print(f"   message: {result.get('message')}")
                    
                    if result.get('success'):
                        data = result.get('data', {})
                        print(f"   尺寸信息: {data.get('size_info')}")
                        print(f"   背景色: {data.get('background_color')}")
                        
                        if 'single_photo' in data:
                            single_len = len(data['single_photo'])
                            print(f"   单张照片: {single_len} 字符")
                        
                        if 'layout_photo' in data and data['layout_photo']:
                            layout_len = len(data['layout_photo'])
                            print(f"   排版照片: {layout_len} 字符")
                        else:
                            print("   排版照片: 无")
                    else:
                        print(f"❌ API返回失败: {result.get('message')}")
                        
                except Exception as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"   原始响应前500字符: {response.text[:500]}")
            else:
                print(f"❌ HTTP请求失败")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data}")
                except:
                    print(f"   原始错误: {response.text[:500]}")
                    
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_api_status():
    """测试API状态"""
    print("\n🔍 测试API状态...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/api/idphoto/status', timeout=10)
        print(f"📥 状态API响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务状态: {result}")
        else:
            print(f"❌ 状态检查失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 状态检查异常: {e}")

if __name__ == '__main__':
    print("🚀 开始测试证件照API...")
    
    test_api_status()
    test_idphoto_api()
    
    print("\n✅ 测试完成!")

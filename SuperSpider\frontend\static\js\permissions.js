/**
 * 权限管理前端脚本
 * 处理用户权限检查和界面控制
 */

class PermissionManager {
    constructor() {
        this.userPermissions = null;
        this.userRole = null;
        this.init();
    }

    async init() {
        await this.loadUserPermissions();
        this.updateUI();

        // 启动定期检查用户状态（每5分钟检查一次）
        this.startPeriodicCheck();
    }

    /**
     * 启动定期检查用户状态
     */
    startPeriodicCheck() {
        setInterval(async () => {
            const oldRole = this.userRole;
            const oldIsProValid = this.isProValid;

            await this.loadUserPermissions();

            // 检查用户状态是否发生变化
            if (oldRole !== this.userRole || oldIsProValid !== this.isProValid) {
                console.log('检测到用户权限状态变化，更新界面');
                this.updateUI();

                // 如果Pro用户过期，显示提示
                if (oldRole === 'pro_user' && this.userRole === 'normal_user') {
                    this.showExpiredNotice();
                }
            }
        }, 5 * 60 * 1000); // 5分钟检查一次
    }

    /**
     * 显示Pro用户过期提示
     */
    showExpiredNotice() {
        // 创建提示框
        const notice = document.createElement('div');
        notice.className = 'expired-notice';
        notice.innerHTML = `
            <div class="notice-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>您的Pro会员已过期，已自动降级为普通用户</span>
                <button class="notice-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 添加样式
        notice.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notice);

        // 5秒后自动消失
        setTimeout(() => {
            if (notice.parentElement) {
                notice.remove();
            }
        }, 5000);
    }

    /**
     * 加载用户权限信息
     */
    async loadUserPermissions() {
        try {
            const response = await fetch('/api/permission/check', {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.userPermissions = data.data.permissions;
                    this.userRole = data.data.role;
                    this.userRoleDisplay = data.data.role_display;
                    this.isBanned = data.data.is_banned;
                    this.isProValid = data.data.is_pro_valid;
                    this.vipExpireDate = data.data.vip_expire_date;
                }
            }
        } catch (error) {
            console.error('加载用户权限失败:', error);
        }
    }

    /**
     * 检查用户是否有特定平台权限
     */
    hasPermission(platform) {
        if (!this.userPermissions) return false;

        const videoPlatforms = this.userPermissions.video_platforms || [];
        const articlePlatforms = this.userPermissions.article_platforms || [];
        const allPlatforms = [...videoPlatforms, ...articlePlatforms];

        return allPlatforms.includes(platform);
    }

    /**
     * 检查用户是否有管理员权限
     */
    hasAdminPermission(permission) {
        if (!this.userPermissions) return false;

        const adminFunctions = this.userPermissions.admin_functions || [];
        return adminFunctions.includes(permission);
    }

    /**
     * 获取用户角色显示名称
     */
    getRoleDisplay() {
        return this.userRoleDisplay || '未知角色';
    }

    /**
     * 检查用户是否被封禁
     */
    isBannedUser() {
        return this.isBanned === true;
    }

    /**
     * 检查Pro用户是否有效
     */
    isProUserValid() {
        return this.isProValid === true;
    }

    /**
     * 更新界面显示
     */
    updateUI() {
        // 延迟更新UI，确保不干扰其他脚本的初始化
        setTimeout(() => {
            this.updatePlatformButtons();
            this.updateUserInfo();
            this.updateAdminFeatures();
        }, 200);
    }

    /**
     * 更新平台按钮状态
     */
    updatePlatformButtons() {
        // 视频平台按钮
        const videoPlatforms = ['douyin', 'kuaishou', 'bilibili'];
        videoPlatforms.forEach(platform => {
            // 只选择平台选择按钮，不包括标签页按钮
            const buttons = document.querySelectorAll(`[data-platform="${platform}"].platform-select-btn`);
            buttons.forEach(button => {
                if (this.hasPermission(platform)) {
                    button.disabled = false;
                    button.classList.remove('disabled');
                } else {
                    button.disabled = true;
                    button.classList.add('disabled');
                }
            });
        });

        // 文章平台按钮
        const articlePlatforms = ['csdn', 'zhihu'];
        articlePlatforms.forEach(platform => {
            // 只选择平台选择按钮，不包括标签页按钮
            const buttons = document.querySelectorAll(`[data-platform="${platform}"].platform-select-btn`);
            buttons.forEach(button => {
                if (this.hasPermission(platform)) {
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.title = '';
                } else {
                    button.disabled = true;
                    button.classList.add('disabled');
                    button.title = '此功能仅限Pro用户使用';
                }
            });
        });

        // 确保非平台相关的按钮不受影响
        this.preserveNonPlatformButtons();
    }

    /**
     * 保护非平台相关的按钮不受权限系统影响
     */
    preserveNonPlatformButtons() {
        // 确保"了解更多"按钮不被禁用
        const morePlatformsBtn = document.getElementById('more-platforms-btn');
        if (morePlatformsBtn) {
            morePlatformsBtn.disabled = false;
            morePlatformsBtn.classList.remove('disabled');
        }

        // 确保"提交反馈"按钮不被禁用
        const feedbackBtn = document.getElementById('feedback-btn');
        if (feedbackBtn) {
            feedbackBtn.disabled = false;
            feedbackBtn.classList.remove('disabled');
        }

        // 确保标签页按钮不被禁用
        const tabButtons = document.querySelectorAll('.platform-tab-btn');
        tabButtons.forEach(button => {
            button.disabled = false;
            button.classList.remove('disabled');
        });
    }

    /**
     * 更新用户信息显示
     */
    updateUserInfo() {
        const userRoleElement = document.getElementById('user-role');
        if (userRoleElement) {
            userRoleElement.textContent = this.getRoleDisplay();

            // 根据角色设置样式
            userRoleElement.className = 'user-role';
            if (this.userRole === 'super_admin') {
                userRoleElement.classList.add('role-admin');
            } else if (this.userRole === 'pro_user') {
                userRoleElement.classList.add('role-pro');
            } else {
                userRoleElement.classList.add('role-normal');
            }
        }

        // 显示Pro用户到期时间
        if (this.userRole === 'pro_user' && this.vipExpireDate) {
            const expireElement = document.getElementById('vip-expire');
            if (expireElement) {
                const expireDate = new Date(this.vipExpireDate);
                expireElement.textContent = `到期时间: ${expireDate.toLocaleDateString()}`;
                expireElement.style.display = 'block';
            }
        }

        // 显示封禁状态
        if (this.isBannedUser()) {
            const bannedElement = document.getElementById('banned-notice');
            if (bannedElement) {
                bannedElement.style.display = 'block';
            }
        }
    }

    /**
     * 更新管理员功能
     */
    updateAdminFeatures() {
        const adminPanel = document.getElementById('admin-panel');
        if (adminPanel) {
            if (this.userRole === 'super_admin') {
                adminPanel.style.display = 'block';
            } else {
                adminPanel.style.display = 'none';
            }
        }
    }

    /**
     * 显示权限不足提示
     */
    showPermissionDenied(platform) {
        let message = '权限不足';

        if (['csdn', 'zhihu'].includes(platform)) {
            message = '此功能仅限Pro用户使用，请升级账户';
        } else if (this.isBannedUser()) {
            message = '账户已被封禁，无法使用此功能';
        } else if (this.userRole === 'pro_user' && !this.isProUserValid()) {
            message = 'Pro会员已过期，请续费后使用';
        }

        // 显示提示消息
        this.showMessage(message, 'error');
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;

        // 添加到页面
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    /**
     * 检查并执行平台操作
     */
    async executePlatformAction(platform, action) {
        if (this.isBannedUser()) {
            this.showPermissionDenied(platform);
            return false;
        }

        if (!this.hasPermission(platform)) {
            this.showPermissionDenied(platform);
            return false;
        }

        if (this.userRole === 'pro_user' && !this.isProUserValid()) {
            this.showPermissionDenied(platform);
            return false;
        }

        // 执行操作
        return await action();
    }

    /**
     * 获取可用平台列表
     */
    async getAvailablePlatforms() {
        try {
            const response = await fetch('/api/permission/platforms', {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                return data.data;
            }
        } catch (error) {
            console.error('获取平台列表失败:', error);
        }
        return null;
    }
}

// 全局权限管理器实例
let permissionManager = null;

// 页面加载完成后初始化权限管理器
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化权限管理器，确保标签页等其他功能先初始化
    setTimeout(() => {
        permissionManager = new PermissionManager();
    }, 500);
});

// 导出权限检查函数供其他脚本使用
window.checkPermission = function(platform) {
    return permissionManager ? permissionManager.hasPermission(platform) : false;
};

window.executeWithPermission = function(platform, action) {
    return permissionManager ? permissionManager.executePlatformAction(platform, action) : false;
};

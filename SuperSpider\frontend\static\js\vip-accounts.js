// VIP账号管理JavaScript

let accounts = [];
let currentEditingId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAccounts();
    loadStats();
    
    // 绑定表单提交事件
    document.getElementById('account-form').addEventListener('submit', handleFormSubmit);
});

// 加载账号列表
async function loadAccounts() {
    try {
        const response = await fetch('/api/vip-accounts/');
        const result = await response.json();
        
        if (result.success) {
            accounts = result.data.accounts;
            renderAccounts();
        } else {
            showMessage('加载账号列表失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 加载统计信息
async function loadStats() {
    try {
        const response = await fetch('/api/vip-accounts/pool-status?platform=csdn');
        const result = await response.json();
        
        if (result.success) {
            const stats = result.data;
            document.getElementById('total-accounts').textContent = stats.total_accounts;
            document.getElementById('active-accounts').textContent = stats.available_accounts;
            document.getElementById('csdn-accounts').textContent = stats.total_accounts;
            
            // 计算今日使用量
            const dailyUsage = stats.accounts_detail.reduce((sum, acc) => sum + acc.daily_uses, 0);
            document.getElementById('daily-usage').textContent = dailyUsage;
        }
    } catch (error) {
        console.error('加载统计信息失败:', error);
    }
}

// 渲染账号列表
function renderAccounts() {
    const grid = document.getElementById('accounts-grid');
    grid.innerHTML = '';
    
    const filteredAccounts = getFilteredAccounts();
    
    if (filteredAccounts.length === 0) {
        grid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无账号数据</div>';
        return;
    }
    
    filteredAccounts.forEach(account => {
        const card = createAccountCard(account);
        grid.appendChild(card);
    });
}

// 创建账号卡片
function createAccountCard(account) {
    const card = document.createElement('div');
    card.className = 'account-card';
    
    const statusClass = `status-${account.status}`;
    const statusText = getStatusText(account.status);
    
    card.innerHTML = `
        <div class="account-header">
            <span class="account-platform">${account.platform.toUpperCase()}</span>
            <span class="account-status ${statusClass}">${statusText}</span>
        </div>
        
        <div class="account-info">
            <div><strong>用户名:</strong> ${account.username}</div>
            <div><strong>VIP等级:</strong> ${account.vip_level || '未知'}</div>
            <div><strong>今日使用:</strong> ${account.daily_uses}/${account.daily_limit}</div>
            <div><strong>并发数:</strong> ${account.current_concurrent}/${account.concurrent_limit}</div>
            <div><strong>健康状态:</strong> ${getHealthStatusText(account.health_status)}</div>
            ${account.expire_date ? `<div><strong>到期时间:</strong> ${formatDate(account.expire_date)}</div>` : ''}
        </div>
        
        <div class="account-actions">
            <button class="btn btn-sm btn-primary" onclick="editAccount(${account.id})">
                <i class="fas fa-edit"></i> 编辑
            </button>
            <button class="btn btn-sm btn-warning" onclick="toggleAccountStatus(${account.id})">
                <i class="fas fa-power-off"></i> ${account.status === 'active' ? '禁用' : '启用'}
            </button>
            <button class="btn btn-sm btn-success" onclick="testAccount(${account.id})">
                <i class="fas fa-check"></i> 测试
            </button>
            <button class="btn btn-sm btn-danger" onclick="deleteAccount(${account.id})">
                <i class="fas fa-trash"></i> 删除
            </button>
        </div>
    `;
    
    return card;
}

// 获取过滤后的账号列表
function getFilteredAccounts() {
    const platformFilter = document.getElementById('platform-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    
    return accounts.filter(account => {
        if (platformFilter && account.platform !== platformFilter) return false;
        if (statusFilter && account.status !== statusFilter) return false;
        return true;
    });
}

// 显示添加模态框
function showAddModal() {
    currentEditingId = null;
    document.getElementById('modal-title').textContent = '添加VIP账号';
    document.getElementById('account-form').reset();
    document.getElementById('account-id').value = '';
    document.getElementById('account-modal').style.display = 'block';
}

// 编辑账号
function editAccount(id) {
    const account = accounts.find(acc => acc.id === id);
    if (!account) return;
    
    currentEditingId = id;
    document.getElementById('modal-title').textContent = '编辑VIP账号';
    
    // 填充表单
    document.getElementById('account-id').value = account.id;
    document.getElementById('platform').value = account.platform;
    document.getElementById('username').value = account.username;
    document.getElementById('email').value = account.email || '';
    document.getElementById('vip-level').value = account.vip_level || '';
    document.getElementById('daily-limit').value = account.daily_limit;
    document.getElementById('notes').value = account.notes || '';
    
    if (account.expire_date) {
        const date = new Date(account.expire_date);
        document.getElementById('expire-date').value = date.toISOString().slice(0, 16);
    }
    
    document.getElementById('account-modal').style.display = 'block';
}

// 关闭模态框
function closeModal() {
    document.getElementById('account-modal').style.display = 'none';
}

// 处理表单提交
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        platform: formData.get('platform'),
        username: formData.get('username'),
        email: formData.get('email'),
        vip_level: formData.get('vip-level'),
        daily_limit: parseInt(formData.get('daily-limit')),
        notes: formData.get('notes')
    };
    
    // 只在添加时包含密码
    if (!currentEditingId) {
        data.password = formData.get('password');
    }
    
    const expireDate = formData.get('expire-date');
    if (expireDate) {
        data.expire_date = new Date(expireDate).toISOString();
    }
    
    try {
        let url = '/api/vip-accounts/';
        let method = 'POST';
        
        if (currentEditingId) {
            url += currentEditingId;
            method = 'PUT';
        }
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(currentEditingId ? '更新成功' : '添加成功', 'success');
            closeModal();
            loadAccounts();
            loadStats();
        } else {
            showMessage('操作失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 切换账号状态
async function toggleAccountStatus(id) {
    const account = accounts.find(acc => acc.id === id);
    if (!account) return;
    
    const newStatus = account.status === 'active' ? 'inactive' : 'active';
    
    try {
        const response = await fetch(`/api/vip-accounts/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: newStatus })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('状态更新成功', 'success');
            loadAccounts();
            loadStats();
        } else {
            showMessage('状态更新失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 测试账号
async function testAccount(id) {
    try {
        const response = await fetch('/api/vip-accounts/health-check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ account_id: id })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('健康检查已启动', 'success');
            setTimeout(() => {
                loadAccounts();
                loadStats();
            }, 2000);
        } else {
            showMessage('测试失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 删除账号
async function deleteAccount(id) {
    if (!confirm('确定要删除这个账号吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/vip-accounts/${id}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('删除成功', 'success');
            loadAccounts();
            loadStats();
        } else {
            showMessage('删除失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 过滤账号
function filterAccounts() {
    renderAccounts();
}

// 刷新账号列表
function refreshAccounts() {
    loadAccounts();
    loadStats();
}

// 工具函数
function getStatusText(status) {
    const statusMap = {
        'active': '活跃',
        'inactive': '不可用',
        'banned': '被封禁',
        'expired': '已过期',
        'maintenance': '维护中'
    };
    return statusMap[status] || status;
}

function getHealthStatusText(status) {
    const statusMap = {
        'healthy': '健康',
        'unhealthy': '不健康',
        'unknown': '未知'
    };
    return statusMap[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showMessage(message, type) {
    // 简单的消息提示，可以后续改进
    const className = type === 'success' ? 'alert-success' : 'alert-error';
    
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `alert ${className}`;
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        z-index: 9999;
        max-width: 300px;
    `;
    
    if (type === 'success') {
        messageEl.style.backgroundColor = '#28a745';
    } else {
        messageEl.style.backgroundColor = '#dc3545';
    }
    
    messageEl.textContent = message;
    document.body.appendChild(messageEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 3000);
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索历史记录弹窗测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="frontend/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-btn {
            padding: 12px 24px;
            background: #7c3aed;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-btn:hover {
            background: #6025ca;
        }
    </style>
</head>
<body>
    <h1>搜索历史记录弹窗测试</h1>
    <button class="test-btn" onclick="showTestModal()">显示搜索历史记录弹窗</button>
    <button class="test-btn" onclick="populateTestData()">填充测试数据</button>

    <!-- 搜索历史记录模态框 -->
    <div class="modal" id="downloads-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container modal-large">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-history"></i>
                    <h2>搜索历史记录</h2>
                </div>
                <div class="modal-header-actions">
                    <button id="export-downloads-btn" class="btn btn-secondary" title="导出搜索记录">
                        <i class="fas fa-file-export"></i> 导出
                    </button>
                    <button class="close-modal"><i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="downloads-container">
                    <!-- 统计卡片 -->
                    <div class="stats-cards-container">
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="stats-card-content">
                                <div class="stats-card-value" id="total-searches">156</div>
                                <div class="stats-card-label">总搜索数</div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <i class="fas fa-calendar-week"></i>
                            </div>
                            <div class="stats-card-content">
                                <div class="stats-card-value" id="recent-searches">23</div>
                                <div class="stats-card-label">最近7天</div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stats-card-content">
                                <div class="stats-card-value" id="favorite-count">12</div>
                                <div class="stats-card-label">收藏</div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stats-card-content">
                                <div class="stats-card-value" id="success-rate">94%</div>
                                <div class="stats-card-label">成功率</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作工具栏 -->
                    <div class="action-toolbar">
                        <div class="toolbar-left">
                            <div class="selection-info">
                                <span class="selected-count-label">
                                    已选择 <strong id="selected-count">0</strong> 项
                                </span>
                                <div class="batch-actions">
                                    <button class="btn btn-sm batch-action-btn disabled" data-action="favorite" title="批量收藏">
                                        <i class="fas fa-star"></i> 收藏
                                    </button>
                                    <button class="btn btn-sm batch-action-btn disabled" data-action="unfavorite" title="批量取消收藏">
                                        <i class="fas fa-star-half-alt"></i> 取消收藏
                                    </button>
                                    <button class="btn btn-sm batch-action-btn disabled" data-action="delete" title="批量删除">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="toolbar-right">
                            <div class="view-controls">
                                <!-- 视图切换 -->
                                <div class="view-mode-switcher">
                                    <button class="view-mode-btn active" data-mode="list" title="列表视图">
                                        <i class="fas fa-list"></i>
                                    </button>
                                    <button class="view-mode-btn" data-mode="grid" title="网格视图">
                                        <i class="fas fa-th-large"></i>
                                    </button>
                                </div>
                                
                                <!-- 排序控制 -->
                                <div class="sort-control">
                                    <label for="sort-select"><i class="fas fa-sort"></i> 排序:</label>
                                    <select id="sort-select">
                                        <option value="created_at-desc">最新添加</option>
                                        <option value="created_at-asc">最早添加</option>
                                        <option value="title-asc">标题 A-Z</option>
                                        <option value="title-desc">标题 Z-A</option>
                                        <option value="search_count-desc">搜索次数</option>
                                        <option value="last_searched_at-desc">最近搜索</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选面板 -->
                    <div class="filter-panel">
                        <div class="filter-header">
                            <h3><i class="fas fa-filter"></i> 筛选条件</h3>
                            <button class="filter-toggle-btn" id="filter-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="filter-content" id="filter-content">
                            <form id="download-filter-form" class="filter-form">
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label for="filter-platform">
                                            <i class="fas fa-tv"></i> 平台
                                        </label>
                                        <select id="filter-platform" name="platform">
                                            <option value="">全部平台</option>
                                            <option value="douyin">抖音</option>
                                            <option value="kuaishou">快手</option>
                                            <option value="bilibili">哔哩哔哩</option>
                                            <option value="csdn">CSDN</option>
                                            <option value="zhihu">知乎</option>
                                            <option value="weibo">微博</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label for="filter-content-type">
                                            <i class="fas fa-file-alt"></i> 类型
                                        </label>
                                        <select id="filter-content-type" name="content_type">
                                            <option value="">全部类型</option>
                                            <option value="video">视频</option>
                                            <option value="article">文章</option>
                                            <option value="resource">资源</option>
                                            <option value="image">图片</option>
                                            <option value="audio">音频</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label for="filter-status">
                                            <i class="fas fa-check-circle"></i> 状态
                                        </label>
                                        <select id="filter-status" name="status">
                                            <option value="">全部状态</option>
                                            <option value="success">成功</option>
                                            <option value="failed">失败</option>
                                        </select>
                                    </div>
                                    <div class="filter-group checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="filter-favorite" name="favorite">
                                            <span class="checkmark"></span>
                                            <i class="fas fa-star"></i> 只显示收藏
                                        </label>
                                    </div>
                                    <div class="filter-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> 筛选
                                        </button>
                                        <button type="button" id="reset-filter-btn" class="btn btn-secondary">
                                            <i class="fas fa-undo"></i> 重置
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 搜索记录列表容器 -->
                    <div class="records-container">
                        <!-- 搜索记录列表 -->
                        <div id="downloads-list" class="records-list">
                            <!-- 这里会动态生成搜索记录列表 -->
                        </div>

                        <!-- 分页导航 -->
                        <div id="downloads-pagination" class="pagination-wrapper">
                            <!-- 动态生成的分页 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTestModal() {
            const modal = document.getElementById('downloads-modal');
            modal.classList.add('active');
            
            // 初始化筛选面板
            initializeFilterPanel();
            
            // 填充测试数据
            populateTestData();
        }

        function initializeFilterPanel() {
            const filterToggle = document.getElementById('filter-toggle');
            const filterContent = document.getElementById('filter-content');
            
            if (filterToggle && filterContent) {
                // 默认展开筛选面板
                filterContent.classList.add('expanded');
                filterToggle.classList.add('expanded');
                filterContent.style.display = 'block';
                
                filterToggle.addEventListener('click', function() {
                    const isExpanded = filterContent.classList.contains('expanded');
                    
                    if (isExpanded) {
                        filterContent.classList.remove('expanded');
                        filterToggle.classList.remove('expanded');
                        filterContent.style.display = 'none';
                    } else {
                        filterContent.classList.add('expanded');
                        filterToggle.classList.add('expanded');
                        filterContent.style.display = 'block';
                    }
                });
            }
        }

        function populateTestData() {
            const downloadsList = document.getElementById('downloads-list');
            downloadsList.innerHTML = `
                <table class="downloads-table">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                <input type="checkbox" id="select-all-checkbox">
                            </th>
                            <th>标题</th>
                            <th>平台</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>搜索时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="status-completed">
                            <td class="checkbox-column">
                                <input type="checkbox" class="download-checkbox" data-id="1">
                            </td>
                            <td class="download-title">精彩短视频合集</td>
                            <td>抖音</td>
                            <td><i class="fas fa-video"></i> 视频</td>
                            <td><span class="status-badge completed">成功</span></td>
                            <td>2024-01-15 14:30</td>
                            <td class="download-actions">
                                <button class="action-btn favorite-btn active" title="取消收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="action-btn notes-btn" title="添加笔记">
                                    <i class="fas fa-sticky-note"></i>
                                </button>
                                <a href="#" class="view-btn" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="action-btn delete-btn" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="status-completed">
                            <td class="checkbox-column">
                                <input type="checkbox" class="download-checkbox" data-id="2">
                            </td>
                            <td class="download-title">技术文章分享</td>
                            <td>CSDN</td>
                            <td><i class="fas fa-file-alt"></i> 文章</td>
                            <td><span class="status-badge completed">成功</span></td>
                            <td>2024-01-14 09:15</td>
                            <td class="download-actions">
                                <button class="action-btn favorite-btn" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="action-btn notes-btn has-notes" title="编辑笔记">
                                    <i class="fas fa-sticky-note"></i>
                                </button>
                                <a href="#" class="view-btn" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="action-btn delete-btn" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="status-failed">
                            <td class="checkbox-column">
                                <input type="checkbox" class="download-checkbox" data-id="3">
                            </td>
                            <td class="download-title">音乐MV下载</td>
                            <td>哔哩哔哩</td>
                            <td><i class="fas fa-music"></i> 音频</td>
                            <td><span class="status-badge failed">失败</span></td>
                            <td>2024-01-13 16:45</td>
                            <td class="download-actions">
                                <button class="action-btn favorite-btn" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="action-btn notes-btn" title="添加笔记">
                                    <i class="fas fa-sticky-note"></i>
                                </button>
                                <a href="#" class="view-btn" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="action-btn delete-btn" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;

            // 分页
            const pagination = document.getElementById('downloads-pagination');
            pagination.innerHTML = `
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 1-10 条，共 156 条记录
                    </div>
                    <div class="pagination-links">
                        <a href="#" class="page-link disabled">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                        <a href="#" class="page-link active">1</a>
                        <a href="#" class="page-link">2</a>
                        <a href="#" class="page-link">3</a>
                        <span>...</span>
                        <a href="#" class="page-link">16</a>
                        <a href="#" class="page-link">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            `;
        }

        // 关闭模态框
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('close-modal') || 
                e.target.closest('.close-modal')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.remove('active');
                }
            }
            
            // 点击遮罩层关闭模态框
            if (e.target.classList.contains('modal-overlay')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.remove('active');
                }
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    activeModal.classList.remove('active');
                }
            }
        });
    </script>
</body>
</html>

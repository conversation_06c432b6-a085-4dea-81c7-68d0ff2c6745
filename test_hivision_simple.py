#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试HivisionIDPhotos API
"""

import requests
import os

def test_with_real_image():
    """使用真实图片测试"""
    print("🔍 使用真实图片测试HivisionIDPhotos...")
    
    # 查找测试图片
    test_image_paths = [
        "SuperSpider/backend/utils/HivisionIDPhotos-master/demo/images/test0.jpg",
        "SuperSpider/backend/utils/HivisionIDPhotos-master/demo/images/test.jpg",
        "test.jpg",
        "demo.jpg"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            print(f"✅ 找到测试图片: {path}")
            break
    
    if not test_image:
        print("❌ 未找到测试图片")
        return
    
    try:
        # 使用最简参数测试
        with open(test_image, 'rb') as f:
            files = {'input_image': f}
            
            print("📤 发送最简请求...")
            response = requests.post('http://127.0.0.1:8080/idphoto', files=files, timeout=30)
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print("✅ JSON解析成功")
                    print(f"   返回字段: {list(result.keys())}")
                    print(f"   状态: {result.get('status', 'N/A')}")
                except Exception as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"   原始响应: {response.text[:500]}...")
            else:
                print(f"❌ 请求失败")
                print(f"   错误内容: {response.text[:500]}...")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_api_docs():
    """测试API文档"""
    print("\n🔍 测试API文档...")
    
    try:
        response = requests.get('http://127.0.0.1:8080/docs', timeout=5)
        print(f"📥 文档状态: {response.status_code}")
        
        # 测试根路径
        response = requests.get('http://127.0.0.1:8080/', timeout=5)
        print(f"📥 根路径状态: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 文档测试失败: {e}")

if __name__ == '__main__':
    print("🚀 开始简单测试...")
    
    test_api_docs()
    test_with_real_image()
    
    print("\n✅ 测试完成!")

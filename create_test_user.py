#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试用户
"""

import requests
import json

def create_test_user():
    """创建测试用户"""
    print("🔍 创建测试用户...")
    
    # 用户数据
    user_data = {
        "username": "test",
        "password": "test123",
        "phone": "13800138000",
        "sms_code": "123456"  # 这个可能需要真实的验证码
    }
    
    try:
        # 尝试注册
        print("📤 尝试注册用户...")
        response = requests.post('http://127.0.0.1:5000/api/auth/register', 
                               json=user_data, timeout=10)
        
        print(f"📥 注册响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 注册成功: {result}")
        else:
            try:
                error_data = response.json()
                print(f"❌ 注册失败: {error_data}")
            except:
                print(f"❌ 注册失败: {response.text}")
                
    except Exception as e:
        print(f"❌ 注册异常: {e}")

def test_login():
    """测试登录"""
    print("\n🔍 测试登录...")
    
    login_data = {
        "account": "test",
        "password": "test123"
    }
    
    try:
        session = requests.Session()
        
        print("📤 尝试登录...")
        response = session.post('http://127.0.0.1:5000/api/auth/login', 
                              json=login_data, timeout=10)
        
        print(f"📥 登录响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功: {result}")
            
            # 测试认证状态
            print("📤 检查认证状态...")
            auth_response = session.get('http://127.0.0.1:5000/api/auth/check-auth')
            print(f"📥 认证状态: {auth_response.status_code}")
            
            if auth_response.status_code == 200:
                auth_data = auth_response.json()
                print(f"✅ 认证成功: {auth_data}")
                return session
            else:
                print(f"❌ 认证失败: {auth_response.text}")
        else:
            try:
                error_data = response.json()
                print(f"❌ 登录失败: {error_data}")
            except:
                print(f"❌ 登录失败: {response.text}")
                
    except Exception as e:
        print(f"❌ 登录异常: {e}")
    
    return None

def test_idphoto_with_login():
    """使用登录状态测试证件照"""
    print("\n🔍 使用登录状态测试证件照...")
    
    # 先登录
    session = test_login()
    if not session:
        print("❌ 登录失败，无法测试证件照")
        return
    
    # 测试证件照API
    test_image = "SuperSpider/backend/utils/HivisionIDPhotos-master/demo/images/test0.jpg"
    
    try:
        with open(test_image, 'rb') as f:
            files = {'photo': ('test0.jpg', f, 'image/jpeg')}
            data = {
                'size': '1inch',
                'background': 'blue'
            }
            
            print("📤 发送证件照请求...")
            response = session.post('http://127.0.0.1:5000/api/idphoto/create', 
                                  files=files, data=data, timeout=120)
            
            print(f"📥 证件照响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 证件照制作成功: {result.get('message', '')}")
                
                if result.get('success'):
                    data = result.get('data', {})
                    print(f"   尺寸信息: {data.get('size_info', 'N/A')}")
                    if 'single_photo' in data:
                        print(f"   单张照片长度: {len(data['single_photo'])} 字符")
                else:
                    print(f"❌ 制作失败: {result.get('message', 'Unknown error')}")
            else:
                try:
                    error_data = response.json()
                    print(f"❌ 请求失败: {error_data}")
                except:
                    print(f"❌ 请求失败: {response.text[:500]}...")
                    
    except Exception as e:
        print(f"❌ 证件照测试异常: {e}")

if __name__ == '__main__':
    print("🚀 开始创建测试用户和测试证件照功能...")
    
    # 创建测试用户（可能失败，因为需要真实验证码）
    create_test_user()
    
    # 测试登录和证件照功能
    test_idphoto_with_login()
    
    print("\n✅ 测试完成!")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接在数据库中创建测试用户
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'SuperSpider'))

from backend.main import create_app, db
from backend.models.user import User

def create_test_user_direct():
    """直接在数据库中创建测试用户"""
    app = create_app()
    
    with app.app_context():
        # 检查用户是否已存在
        existing_user = User.query.filter_by(username='test').first()
        if existing_user:
            print("✅ 测试用户已存在")
            print(f"   用户名: {existing_user.username}")
            print(f"   手机号: {existing_user.phone}")
            print(f"   是否激活: {existing_user.is_active}")
            return existing_user
        
        # 创建新用户
        try:
            user = User(
                username='test',
                password='test123',  # 构造函数需要password参数
                phone='13800138000',
                is_active=True,
                is_admin=False
            )
            
            db.session.add(user)
            db.session.commit()
            
            print("✅ 测试用户创建成功")
            print(f"   用户名: {user.username}")
            print(f"   手机号: {user.phone}")
            print(f"   用户ID: {user.id}")
            
            return user
            
        except Exception as e:
            print(f"❌ 创建用户失败: {e}")
            db.session.rollback()
            return None

if __name__ == '__main__':
    print("🚀 直接创建测试用户...")
    create_test_user_direct()
    print("✅ 完成!")

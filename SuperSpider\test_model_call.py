#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试HivisionIDPhotos模型调用
"""

import requests
import base64
import io
from PIL import Image
import json

def test_model_call():
    """测试模型是否被正确调用"""
    print('🧪 测试HivisionIDPhotos模型调用...')

    # 创建一个更复杂的测试图像 (带有简单图案)
    img = Image.new('RGB', (300, 400), color=(255, 255, 255))  # 白色背景
    # 添加一个简单的圆形图案模拟人脸
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.ellipse([100, 100, 200, 200], fill=(255, 200, 180))  # 肤色圆形
    draw.ellipse([130, 130, 140, 140], fill=(0, 0, 0))  # 左眼
    draw.ellipse([160, 130, 170, 140], fill=(0, 0, 0))  # 右眼
    draw.ellipse([145, 160, 155, 170], fill=(0, 0, 0))  # 鼻子

    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    # 测试idphoto API
    try:
        files = {'input_image': ('test.jpg', img_bytes, 'image/jpeg')}
        data = {
            'height': 413,
            'width': 295,
            'human_matting_model': 'modnet_photographic_portrait_matting',
            'face_detect_model': 'mtcnn',
            'hd': True,
            'dpi': 300,
            'face_alignment': True
        }
        
        print('📤 发送请求到 HivisionIDPhotos...')
        print(f'   请求URL: http://127.0.0.1:8080/idphoto')
        print(f'   图像尺寸: {img.size}')
        print(f'   抠图模型: {data["human_matting_model"]}')
        print(f'   人脸检测: {data["face_detect_model"]}')
        
        response = requests.post('http://127.0.0.1:8080/idphoto', files=files, data=data, timeout=60)
        print(f'📥 响应状态: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print('✅ API调用成功!')
            print(f'   返回字段: {list(result.keys())}')
            
            # 检查返回的数据
            if 'status' in result:
                print(f'   处理状态: {result["status"]}')
            
            if 'image_base64_standard' in result:
                print('✅ 模型处理成功，返回了标准证件照')
                img_data = result['image_base64_standard']
                print(f'   Base64长度: {len(img_data)} 字符')
            else:
                print('❌ 未返回预期的图像数据')
                print(f'   实际返回: {result}')
                
            if 'image_base64_hd' in result:
                print('✅ 返回了高清证件照')
                
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'   错误信息: {response.text}')

            # 尝试解析错误信息
            try:
                error_data = response.json()
                print(f'   详细错误: {error_data}')
            except:
                print('   无法解析错误响应为JSON')

    except requests.exceptions.Timeout:
        print('❌ 请求超时 - 模型加载可能需要更长时间')
    except Exception as e:
        print(f'❌ 测试异常: {e}')

def test_simple_matting():
    """测试简单的抠图功能"""
    print('\n🎨 测试人像抠图功能...')

    # 创建一个简单的测试图像
    img = Image.new('RGB', (300, 400), color=(255, 255, 255))
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)

    try:
        files = {'input_image': ('test.jpg', img_bytes, 'image/jpeg')}
        data = {
            'human_matting_model': 'modnet_photographic_portrait_matting',
            'dpi': 300
        }

        print('📤 发送抠图请求...')
        response = requests.post('http://127.0.0.1:8080/human_matting', files=files, data=data, timeout=30)
        print(f'📥 抠图响应状态: {response.status_code}')

        if response.status_code == 200:
            result = response.json()
            print('✅ 抠图API调用成功!')
            print(f'   返回字段: {list(result.keys())}')
        else:
            print(f'❌ 抠图API失败: {response.text}')

    except Exception as e:
        print(f'❌ 抠图测试异常: {e}')

def test_simple_api():
    """测试简单的API调用"""
    print('\n🔍 测试基础API连接...')
    try:
        # 测试根路径
        response = requests.get('http://127.0.0.1:8080/', timeout=5)
        print(f'根路径状态: {response.status_code}')
        
        # 测试文档页面
        response = requests.get('http://127.0.0.1:8080/docs', timeout=5)
        print(f'文档页面状态: {response.status_code}')
        
    except Exception as e:
        print(f'基础连接测试失败: {e}')

if __name__ == '__main__':
    test_simple_api()
    test_simple_matting()
    test_model_call()

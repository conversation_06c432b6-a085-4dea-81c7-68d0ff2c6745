#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快手爬虫模块
用于解析快手视频链接，获取视频信息
"""

import json
import logging
import re
import requests
import traceback
from functools import lru_cache
from typing import Dict, Any, Optional
from requests.exceptions import ConnectionError, Timeout

from .base_spider import BaseSpider
from ..utils.settings import KUAI_SHOU_COOKIE

# 创建日志记录器
logger = logging.getLogger(__name__)


@lru_cache(maxsize=20)
def get_final_url(url, cookie=KUAI_SHOU_COOKIE):
    response = requests.get(
        url,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': '*/*',
            'Cookie': cookie
        },
        allow_redirects=True,
        timeout=10
    )
    final_url = response.url.split('?')[0].split('#')[0]
    return final_url


def clean_text(text):
    """清理文本中的特殊字符和多余空格"""
    if not text:
        return ""
    return re.sub(r'[\r\xa0\x00-\x09\x0b-\x0c\x0e-\x1f\x7f-\xa0\u200b-\u200f\u2028-\u202f\u3000]+', '', text).strip()


def parseKsVideo(video_url):
    # 获取最终重定向URL
    final_url = get_final_url(video_url)
    # print(final_url)

    headers = {
        'content-type': 'application/json',
        'cookie': KUAI_SHOU_COOKIE,  # cookie改变了，修改settings中的即可
        'host': 'www.kuaishou.com',
        'origin': 'https://www.kuaishou.com',
        'referer': final_url,
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    response = requests.post(url=final_url, headers=headers)
    html = response.text
    # print(html)

    # 改进的正则表达式匹配
    apollo_match = re.search(r'window\.__APOLLO_STATE__\s*=\s*({.*?});', html, re.S).group(1)
    data = json.loads(apollo_match)
    # print(data)

    # 改进的数据提取方式
    find_node = lambda prefix: next(v for k, v in data['defaultClient'].items() if k.startswith(prefix))

    # 视频标题
    title = clean_text(find_node('VisionVideoDetailPhoto:')['caption'])
    # 视频链接
    videoUrl = clean_text(find_node('VisionVideoDetailPhoto:')['photoUrl'])
    # 作者名字
    author = clean_text(find_node('VisionVideoDetailAuthor:')['name'])

    result = [{
        'title': title,
        'videoUrl': videoUrl,
        'author': author
    }]
    return result


class KuaishouSpider(BaseSpider):
    """
    快手视频爬虫类
    继承自BaseSpider，实现快手视频解析功能
    """

    def __init__(self):
        """初始化快手爬虫"""
        super().__init__("快手爬虫")
        self.platform = "kuaishou"

        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cookie': KUAI_SHOU_COOKIE,
        }

    def _get_user_friendly_error(self, error):
        """将技术错误转换为用户友好的错误信息"""
        error_str = str(error).lower()

        if isinstance(error, (ConnectionError,)) or 'failed to resolve' in error_str:
            return "网络连接失败，请检查网络连接或稍后重试"
        elif isinstance(error, Timeout) or 'timeout' in error_str:
            return "请求超时，请检查网络连接或稍后重试"
        elif 'max retries exceeded' in error_str:
            return "网络请求失败，服务器可能暂时不可用，请稍后重试"
        elif 'ssl' in error_str or 'certificate' in error_str:
            return "安全连接失败，请检查网络设置"
        elif 'permission denied' in error_str:
            return "访问被拒绝，可能是网络限制或防火墙阻止"
        elif 'invalid url' in error_str or 'url' in error_str:
            return "视频链接格式不正确，请检查链接是否完整"
        else:
            return "解析失败，请检查视频链接是否有效或稍后重试"

    def validate_url(self, url: str) -> bool:
        """
        验证快手URL是否有效

        Args:
            url: 待验证的URL

        Returns:
            bool: URL是否有效
        """
        try:
            # 快手URL格式示例:
            # https://v.kuaishou.com/xxx
            # https://www.kuaishou.com/xxx
            kuaishou_patterns = [
                r'kuaishou\.com',
                r'v\.kuaishou\.com'
            ]

            return any(re.search(pattern, url) for pattern in kuaishou_patterns)
        except Exception as e:
            logger.error(f"URL验证失败: {str(e)}")
            return False

    def extract_video_info(self, url: str) -> Dict[str, Any]:
        """
        从快手URL提取视频信息

        Args:
            url: 快手视频URL

        Returns:
            Dict: 包含视频信息的字典
        """
        try:
            # 使用现有的解析函数
            result_list = parseKsVideo(url)

            if result_list and len(result_list) > 0:
                video_data = result_list[0]

                # 转换为标准格式
                result = {
                    'title': video_data.get('title', ''),
                    'author': video_data.get('author', ''),
                    'videoUrl': video_data.get('videoUrl', ''),
                    'coverUrl': '',  # 快手解析暂时没有封面
                    'description': video_data.get('title', ''),  # 使用标题作为描述
                    'duration': 0,  # 快手解析暂时没有时长
                }

                logger.info(f"成功解析快手视频信息: {result['title']}")
                return result
            else:
                raise Exception("无法获取视频信息")

        except Exception as e:
            logger.error(f"提取视频信息失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 抛出用户友好的错误信息
            user_error = self._get_user_friendly_error(e)
            raise Exception(user_error)

    def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行快手爬虫任务

        Args:
            task: 任务参数
                {
                    "video_url": "快手视频URL",
                    "download": false
                }

        Returns:
            Dict: 执行结果
        """
        try:
            video_url = task.get("video_url")

            if not video_url:
                return {
                    "success": False,
                    "message": "请提供视频链接",
                    "data": None
                }

            # 验证URL
            if not self.validate_url(video_url):
                return {
                    "success": False,
                    "message": "请提供有效的快手视频链接",
                    "data": None
                }

            # 提取视频信息
            video_info = self.extract_video_info(video_url)

            if not video_info:
                return {
                    "success": False,
                    "message": "无法获取视频信息，请检查链接是否有效",
                    "data": None
                }

            return {
                "success": True,
                "message": "解析成功",
                "data": [video_info]  # 快手API期望返回数组格式
            }

        except Exception as e:
            logger.error(f"快手爬虫执行失败: {str(e)}")
            logger.error(traceback.format_exc())

            # 返回用户友好的错误信息
            user_error = str(e) if any(keyword in str(e) for keyword in
                                     ['网络连接失败', '请求超时', '视频', '链接', '格式']) else self._get_user_friendly_error(e)

            return {
                "success": False,
                "message": user_error,
                "data": None
            }

    def check_status(self) -> Dict[str, Any]:
        """
        检查快手爬虫状态

        Returns:
            Dict: 状态信息
        """
        return {
            "name": self.name,
            "platform": self.platform,
            "status": "ready",
            "version": "1.0.0"
        }


# 全局爬虫实例（延迟初始化）
_kuaishou_spider = None


def get_kuaishou_spider() -> KuaishouSpider:
    """
    获取快手爬虫实例（延迟初始化）

    Returns:
        KuaishouSpider: 爬虫实例
    """
    global _kuaishou_spider
    if _kuaishou_spider is None:
        _kuaishou_spider = KuaishouSpider()
    return _kuaishou_spider


# if __name__ == '__main__':
#     result = parseKsVideo('https://v.kuaishou.com/2AoT6o8 看一下"cos "鸣潮 "椿cos "漫展')
#     print(result)
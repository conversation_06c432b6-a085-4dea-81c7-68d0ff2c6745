#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VIP账号池管理器
负责管理和分配VIP账号资源
"""

import logging
import random
import threading
from typing import Optional, List, Dict, Any
from contextlib import contextmanager

from ..models.vip_account import VipAccount, AccountStatus
from ..main import db

logger = logging.getLogger(__name__)


class VipAccountPool:
    """VIP账号池管理器"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._platform_pools = {}  # 按平台分组的账号池
        self._refresh_interval = 300  # 5分钟刷新一次
        self._last_refresh = 0
    
    def get_available_account(self, platform: str) -> Optional[VipAccount]:
        """
        获取可用的VIP账号
        
        Args:
            platform: 平台名称 (如 'csdn')
            
        Returns:
            可用的VIP账号对象，如果没有可用账号则返回None
        """
        with self._lock:
            # 刷新账号池
            self._refresh_pool_if_needed(platform)
            
            # 获取平台的可用账号
            available_accounts = self._get_platform_accounts(platform, only_available=True)
            
            if not available_accounts:
                logger.warning(f"没有可用的{platform}账号")
                return None
            
            # 选择使用次数最少的账号（负载均衡）
            account = min(available_accounts, key=lambda x: x.daily_uses)
            
            # 尝试获取账号使用权
            if account.acquire():
                logger.info(f"成功获取{platform}账号: {account.username}")
                return account
            else:
                logger.warning(f"获取{platform}账号失败: {account.username}")
                return None
    
    @contextmanager
    def use_account(self, platform: str):
        """
        上下文管理器，自动管理账号的获取和释放
        
        Args:
            platform: 平台名称
            
        Yields:
            VipAccount对象或None
        """
        account = self.get_available_account(platform)
        try:
            yield account
        finally:
            if account:
                account.release()
                logger.info(f"释放{platform}账号: {account.username}")
    
    def _refresh_pool_if_needed(self, platform: str):
        """如果需要，刷新账号池"""
        import time
        current_time = time.time()
        
        if current_time - self._last_refresh > self._refresh_interval:
            self._refresh_pool(platform)
            self._last_refresh = current_time
    
    def _refresh_pool(self, platform: str):
        """刷新指定平台的账号池"""
        try:
            # 从数据库重新加载账号
            accounts = VipAccount.query.filter_by(platform=platform).all()
            
            # 重置过期的每日计数器
            for account in accounts:
                account._reset_daily_counter_if_needed()
            
            # 更新内存中的账号池
            self._platform_pools[platform] = accounts
            
            logger.info(f"刷新{platform}账号池完成，共{len(accounts)}个账号")
            
        except Exception as e:
            logger.error(f"刷新{platform}账号池失败: {e}")
    
    def _get_platform_accounts(self, platform: str, only_available: bool = False) -> List[VipAccount]:
        """获取指定平台的账号列表"""
        if platform not in self._platform_pools:
            self._refresh_pool(platform)
        
        accounts = self._platform_pools.get(platform, [])
        
        if only_available:
            accounts = [acc for acc in accounts if acc.is_available()]
        
        return accounts
    
    def get_pool_status(self, platform: str) -> Dict[str, Any]:
        """获取账号池状态"""
        accounts = self._get_platform_accounts(platform)
        available_accounts = [acc for acc in accounts if acc.is_available()]
        
        status = {
            'platform': platform,
            'total_accounts': len(accounts),
            'available_accounts': len(available_accounts),
            'accounts_detail': []
        }
        
        for account in accounts:
            status['accounts_detail'].append({
                'id': account.id,
                'username': account.username,
                'status': account.status,
                'daily_uses': account.daily_uses,
                'daily_limit': account.daily_limit,
                'current_concurrent': account.current_concurrent,
                'concurrent_limit': account.concurrent_limit,
                'health_status': account.health_status,
                'is_available': account.is_available()
            })
        
        return status
    
    def health_check(self, platform: str = None):
        """执行健康检查"""
        platforms = [platform] if platform else ['csdn', 'zhihu']
        
        for plat in platforms:
            accounts = self._get_platform_accounts(plat)
            
            for account in accounts:
                try:
                    # 这里可以实现具体的健康检查逻辑
                    # 比如尝试登录、检查VIP状态等
                    self._check_account_health(account)
                except Exception as e:
                    logger.error(f"健康检查失败 {account.username}: {e}")
                    account.mark_error(str(e))
    
    def _check_account_health(self, account: VipAccount):
        """检查单个账号的健康状态"""
        # 这里实现具体的健康检查逻辑
        # 例如：检查登录状态、VIP有效性等
        
        if account.platform == 'csdn':
            # CSDN账号健康检查
            self._check_csdn_account_health(account)
        elif account.platform == 'zhihu':
            # 知乎账号健康检查
            self._check_zhihu_account_health(account)
        
        # 如果检查通过，标记为健康
        account.mark_healthy()
    
    def _check_csdn_account_health(self, account: VipAccount):
        """检查CSDN账号健康状态"""
        import requests
        
        # 构建登录会话
        session = requests.Session()
        
        # 设置请求头
        headers = account.get_headers()
        if headers:
            session.headers.update(headers)
        
        # 设置cookies
        cookies = account.get_cookies()
        if cookies:
            session.cookies.update(cookies)
        
        # 尝试访问CSDN个人中心或VIP页面来验证状态
        try:
            response = session.get('https://passport.csdn.net/account/profile', timeout=10)
            if response.status_code == 200:
                # 检查是否包含VIP相关信息
                if 'vip' in response.text.lower() or '会员' in response.text:
                    logger.info(f"CSDN账号 {account.username} 健康检查通过")
                    return True
            
            raise Exception(f"健康检查失败，状态码: {response.status_code}")
            
        except Exception as e:
            raise Exception(f"CSDN账号健康检查失败: {e}")
    
    def _check_zhihu_account_health(self, account: VipAccount):
        """检查知乎账号健康状态"""
        # 实现知乎账号健康检查逻辑
        pass
    
    def add_account(self, platform: str, username: str, password: str, 
                   created_by: int, **kwargs) -> VipAccount:
        """添加新的VIP账号"""
        try:
            account = VipAccount(
                platform=platform,
                username=username,
                password=password,
                created_by=created_by,
                **kwargs
            )
            
            db.session.add(account)
            db.session.commit()
            
            # 刷新账号池
            self._refresh_pool(platform)
            
            logger.info(f"成功添加{platform}账号: {username}")
            return account
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"添加{platform}账号失败: {e}")
            raise
    
    def remove_account(self, account_id: int):
        """移除VIP账号"""
        try:
            account = VipAccount.query.get(account_id)
            if account:
                platform = account.platform
                db.session.delete(account)
                db.session.commit()
                
                # 刷新账号池
                self._refresh_pool(platform)
                
                logger.info(f"成功移除账号: {account.username}")
            else:
                raise ValueError("账号不存在")
                
        except Exception as e:
            db.session.rollback()
            logger.error(f"移除账号失败: {e}")
            raise
    
    def update_account_status(self, account_id: int, status: str):
        """更新账号状态"""
        try:
            account = VipAccount.query.get(account_id)
            if account:
                account.status = status
                db.session.commit()
                
                # 刷新账号池
                self._refresh_pool(account.platform)
                
                logger.info(f"更新账号状态: {account.username} -> {status}")
            else:
                raise ValueError("账号不存在")
                
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新账号状态失败: {e}")
            raise


# 全局账号池实例
vip_account_pool = VipAccountPool()

/**
 * SuperSpider 搜索历史记录脚本
 * 处理用户搜索历史记录相关功能
 */

// 当前页码
let currentPage = 1;
// 每页记录数
const perPage = 10;
// 当前筛选条件
let currentFilters = {};
// 选中的搜索记录ID
let selectedDownloadIds = [];
// 当前视图模式：'list' 或 'grid'
let currentViewMode = 'list';
// 当前排序方式
let currentSort = { field: 'created_at', order: 'desc' };

// 防抖变量
let loadHistoryTimeout = null;
let loadStatsTimeout = null;

// 初始化搜索历史记录功能
function initDownloadFeatures() {
    console.log('初始化搜索记录功能...');

    // 搜索记录链接点击事件
    const downloadsLink = document.getElementById('downloads-link');
    console.log('搜索记录链接元素:', downloadsLink);

    if (downloadsLink) {
        // 移除可能存在的旧事件监听器
        downloadsLink.removeEventListener('click', handleDownloadsLinkClick);
        // 添加新的事件监听器
        downloadsLink.addEventListener('click', handleDownloadsLinkClick);
        console.log('搜索记录链接事件监听器已添加');
    } else {
        console.error('未找到搜索记录链接元素');
    }
}

// 处理搜索记录链接点击事件
function handleDownloadsLinkClick(e) {
    e.preventDefault();
    console.log('搜索记录链接被点击');
    openModal('downloads-modal');

    // 初始化模态框功能
    setTimeout(() => {
        initDownloadModalFeatures();
        loadDownloadHistory();
    }, 100); // 给模态框一点时间来显示
}

// 继续初始化下载历史记录功能
function continueInitDownloadFeatures() {
    // 筛选表单提交
    const filterForm = document.getElementById('download-filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // 获取筛选条件
            const platform = document.getElementById('filter-platform').value;
            const status = document.getElementById('filter-status').value;
            const contentType = document.getElementById('filter-content-type').value;
            const favorite = document.getElementById('filter-favorite').checked;

            // 更新筛选条件
            currentFilters = {
                platform: platform || undefined,
                status: status || undefined,
                content_type: contentType || undefined
            };

            // 如果选择了只显示收藏
            if (favorite) {
                currentFilters.is_favorite = true;
            }

            // 重置页码并加载数据
            currentPage = 1;
            loadDownloadHistoryDebounced();
        });
    }

    // 重置筛选按钮
    const resetFilterBtn = document.getElementById('reset-filter-btn');
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', () => {
            // 重置筛选表单
            const filterForm = document.getElementById('download-filter-form');
            if (filterForm) {
                filterForm.reset();
            }

            // 清空筛选条件
            currentFilters = {};

            // 重置页码并加载数据
            currentPage = 1;
            loadDownloadHistoryDebounced();
        });
    }

    // 分页按钮
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('page-link')) {
            e.preventDefault();

            const page = parseInt(e.target.dataset.page);
            if (!isNaN(page) && page !== currentPage) {
                currentPage = page;
                loadDownloadHistoryDebounced();
            }
        }
    });

    // 视图切换按钮
    const viewModeButtons = document.querySelectorAll('.view-mode-btn');
    viewModeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const mode = btn.dataset.mode;
            if (mode && mode !== currentViewMode) {
                currentViewMode = mode;

                // 更新按钮状态
                viewModeButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 重新渲染下载列表
                renderDownloadsList(window.lastDownloads || []);
            }
        });
    });

    // 排序下拉菜单
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', () => {
            const value = sortSelect.value;
            const [field, order] = value.split('-');

            if (field && order) {
                currentSort = { field, order };
                loadDownloadHistoryDebounced();
            }
        });
    }

    // 批量操作按钮
    const batchActionButtons = document.querySelectorAll('.batch-action-btn');
    batchActionButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const action = btn.dataset.action;

            if (!selectedDownloadIds.length) {
                showToast('请先选择下载记录', 'warning');
                return;
            }

            if (action === 'delete') {
                if (confirm('确定要删除选中的下载记录吗？')) {
                    batchDeleteDownloads(selectedDownloadIds);
                }
            } else if (action === 'favorite') {
                batchToggleFavorite(selectedDownloadIds, true);
            } else if (action === 'unfavorite') {
                batchToggleFavorite(selectedDownloadIds, false);
            }
        });
    });

    // 全选/取消全选复选框
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', () => {
            const checked = selectAllCheckbox.checked;
            const checkboxes = document.querySelectorAll('.download-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;

                // 更新选中的下载记录ID
                const downloadId = parseInt(checkbox.dataset.id);
                if (checked) {
                    if (!selectedDownloadIds.includes(downloadId)) {
                        selectedDownloadIds.push(downloadId);
                    }
                } else {
                    selectedDownloadIds = selectedDownloadIds.filter(id => id !== downloadId);
                }
            });

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        });
    }

    // 导出按钮
    const exportBtn = document.getElementById('export-downloads-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', () => {
            exportDownloads();
        });
    }
}

// 初始化其他下载功能（当模态框打开时调用）
function initDownloadModalFeatures() {
    // 当下载模态框打开时，初始化其他功能
    const downloadsModal = document.getElementById('downloads-modal');
    if (downloadsModal && !downloadsModal.dataset.initialized) {
        // 标记模态框已初始化，防止重复初始化
        downloadsModal.dataset.initialized = 'true';

        // 初始化其他功能
        continueInitDownloadFeatures();
    }
}

// 防抖版本的加载下载历史记录
function loadDownloadHistoryDebounced(delay = 300) {
    if (loadHistoryTimeout) {
        clearTimeout(loadHistoryTimeout);
    }

    loadHistoryTimeout = setTimeout(() => {
        loadDownloadHistory();
    }, delay);
}

// 防抖版本的加载下载统计
function loadDownloadStatsDebounced(delay = 300) {
    if (loadStatsTimeout) {
        clearTimeout(loadStatsTimeout);
    }

    loadStatsTimeout = setTimeout(() => {
        loadDownloadStats();
    }, delay);
}

// 加载下载历史记录
async function loadDownloadHistory() {
    console.log('开始加载搜索历史记录...');

    const downloadsList = document.getElementById('downloads-list');
    const downloadsPagination = document.getElementById('downloads-pagination');
    const downloadsLoading = document.getElementById('downloads-loading');
    const downloadsEmpty = document.getElementById('downloads-empty');

    console.log('DOM元素检查:', {
        downloadsList: !!downloadsList,
        downloadsPagination: !!downloadsPagination,
        downloadsLoading: !!downloadsLoading,
        downloadsEmpty: !!downloadsEmpty
    });

    if (!downloadsList || !downloadsPagination) {
        console.error('关键DOM元素未找到');
        return;
    }

    try {
        // 显示加载中
        if (downloadsLoading) {
            downloadsLoading.style.display = 'block';
        }

        // 隐藏空状态
        if (downloadsEmpty) {
            downloadsEmpty.style.display = 'none';
        }

        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            per_page: perPage,
            sort_field: currentSort.field,
            sort_order: currentSort.order
        });

        // 添加筛选条件
        Object.entries(currentFilters).forEach(([key, value]) => {
            if (value !== undefined) {
                params.append(key, value);
            }
        });

        // 发送请求
        let url = '/api/search/history';
        if (currentFilters.is_favorite) {
            url = '/api/search/favorites';
        }

        const fullUrl = `${url}?${params.toString()}`;
        console.log('发送API请求:', fullUrl);

        const response = await fetch(fullUrl);
        console.log('API响应状态:', response.status);

        // 检查响应状态
        if (response.status === 401) {
            // 未登录，显示登录提示
            if (downloadsLoading) {
                downloadsLoading.style.display = 'none';
            }

            downloadsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-user-lock"></i>
                    <h3>需要登录</h3>
                    <p>请先登录后查看搜索记录</p>
                    <button class="btn btn-primary" onclick="showLoginModal()">立即登录</button>
                </div>
            `;

            // 清空分页
            downloadsPagination.innerHTML = '';
            return;
        }

        const data = await response.json();

        // 隐藏加载中
        if (downloadsLoading) {
            downloadsLoading.style.display = 'none';
        }

        if (data.success) {
            const { records, total, page, pages } = data.data;

            // 保存搜索记录，用于视图切换
            window.lastDownloads = records;

            // 更新当前页码
            currentPage = page;

            // 检查是否有数据
            if (records.length === 0) {
                // 隐藏空状态元素（如果存在）
                if (downloadsEmpty) {
                    downloadsEmpty.style.display = 'none';
                }

                // 显示友好的空状态消息
                downloadsList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>暂无搜索记录</h3>
                        <p>您还没有进行过任何搜索，快去试试吧！</p>
                        <button class="btn btn-primary" onclick="closeModal('downloads-modal')">开始搜索</button>
                    </div>
                `;
                downloadsPagination.innerHTML = '';
                return;
            }

            // 渲染搜索列表
            renderDownloadsList(records);

            // 渲染分页
            renderPagination(page, pages, total);

            // 只在首次加载时加载搜索统计，避免重复请求
            if (currentPage === 1 && Object.keys(currentFilters).length === 0) {
                loadDownloadStats();
            }

            // 重置选中的搜索记录ID
            selectedDownloadIds = [];

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        } else {
            console.error('获取搜索历史记录失败:', data.message);

            // 显示错误消息
            downloadsList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>获取搜索历史记录失败: ${data.message}</p>
                </div>
            `;

            // 清空分页
            downloadsPagination.innerHTML = '';
        }
    } catch (error) {
        console.error('获取搜索历史记录请求失败:', error);

        // 隐藏加载中
        if (downloadsLoading) {
            downloadsLoading.style.display = 'none';
        }

        // 显示错误消息
        downloadsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>获取搜索历史记录请求失败，请稍后再试</p>
            </div>
        `;

        // 清空分页
        downloadsPagination.innerHTML = '';
    }
}

// 渲染下载列表
function renderDownloadsList(downloads) {
    const downloadsList = document.getElementById('downloads-list');
    if (!downloadsList) return;

    // 清空列表
    downloadsList.innerHTML = '';

    // 根据视图模式渲染
    if (currentViewMode === 'grid') {
        renderGridView(downloads, downloadsList);
    } else {
        renderListView(downloads, downloadsList);
    }
}

// 渲染列表视图
function renderListView(downloads, container) {
    // 创建表格
    const table = document.createElement('table');
    table.className = 'downloads-table';

    // 创建表头
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th class="checkbox-column">
                <input type="checkbox" id="select-all-checkbox">
            </th>
            <th>标题</th>
            <th>平台</th>
            <th>类型</th>
            <th>状态</th>
            <th>时间</th>
            <th>操作</th>
        </tr>
    `;
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement('tbody');

    downloads.forEach(download => {
        const tr = document.createElement('tr');

        // 设置状态类名
        if (download.status === 'success') {
            tr.classList.add('status-completed');
        } else if (download.status === 'failed') {
            tr.classList.add('status-failed');
        }

        // 如果是收藏，添加收藏类名
        if (download.is_favorite) {
            tr.classList.add('is-favorite');
        }

        // 设置行内容
        tr.innerHTML = `
            <td class="checkbox-column">
                <input type="checkbox" class="download-checkbox" data-id="${download.id}">
            </td>
            <td class="download-title" title="${download.title || '未知标题'}">${download.title || '未知标题'}</td>
            <td>${getPlatformLabel(download.platform)}</td>
            <td>${getContentTypeLabel(download.content_type)}</td>
            <td>${getStatusLabel(download.status)}</td>
            <td>${formatDate(download.created_at)}</td>
            <td>
                <div class="download-actions">
                    ${download.video_url ?
                        `<a href="${download.video_url}" target="_blank" class="view-btn" title="查看视频">
                            <i class="fas fa-external-link-alt"></i>
                        </a>` : ''
                    }
                    <button class="action-btn favorite-btn ${download.is_favorite ? 'active' : ''}"
                            data-id="${download.id}"
                            title="${download.is_favorite ? '取消收藏' : '收藏'}"
                            onclick="toggleFavorite(${download.id})">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="action-btn notes-btn ${download.notes ? 'has-notes' : ''}"
                            data-id="${download.id}"
                            title="${download.notes ? '编辑笔记' : '添加笔记'}"
                            onclick="showNotesModal(${download.id}, '${download.title || '未知标题'}')">
                        <i class="fas fa-sticky-note"></i>
                    </button>
                    <button class="action-btn delete-btn"
                            data-id="${download.id}"
                            title="删除"
                            onclick="deleteSearchRecord(${download.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    container.appendChild(table);

    // 添加复选框事件
    addCheckboxEvents();
}

// 渲染网格视图
function renderGridView(downloads, container) {
    const grid = document.createElement('div');
    grid.className = 'downloads-grid';

    downloads.forEach(download => {
        const card = document.createElement('div');
        card.className = 'download-card';

        // 设置状态类名
        if (download.status === 'success') {
            card.classList.add('status-completed');
        } else if (download.status === 'failed') {
            card.classList.add('status-failed');
        }

        // 如果是收藏，添加收藏类名
        if (download.is_favorite) {
            card.classList.add('is-favorite');
        }

        // 缩略图
        let thumbnailHtml = '';
        if (download.thumbnail_url) {
            thumbnailHtml = `<img src="${download.thumbnail_url}" alt="${download.title || '未知标题'}" class="download-thumbnail">`;
        } else {
            thumbnailHtml = `<div class="download-thumbnail-placeholder">
                <i class="${getContentTypeIcon(download.content_type)}"></i>
            </div>`;
        }

        // 设置卡片内容
        card.innerHTML = `
            <div class="download-card-header">
                <div class="download-checkbox-container">
                    <input type="checkbox" class="download-checkbox" data-id="${download.id}">
                </div>
                <div class="download-card-platform">${getPlatformLabel(download.platform)}</div>
                ${download.is_favorite ? '<div class="download-card-favorite"><i class="fas fa-star"></i></div>' : ''}
            </div>
            <div class="download-card-thumbnail">
                ${thumbnailHtml}
            </div>
            <div class="download-card-content">
                <h3 class="download-card-title" title="${download.title || '未知标题'}">${download.title || '未知标题'}</h3>
                <div class="download-card-author">${download.author || '未知作者'}</div>
                <div class="download-card-meta">
                    <span class="download-card-type">${getContentTypeLabel(download.content_type)}</span>
                    <span class="download-card-status">${getStatusLabel(download.status)}</span>
                </div>
                <div class="download-card-date">${formatDate(download.created_at)}</div>
                ${download.notes ? '<div class="download-card-notes-indicator"><i class="fas fa-sticky-note"></i> 有笔记</div>' : ''}
            </div>
            <div class="download-card-actions">
                ${download.video_url ?
                    `<a href="${download.video_url}" target="_blank" class="view-btn" title="查看视频">
                        <i class="fas fa-external-link-alt"></i>
                    </a>` : ''
                }
                <button class="action-btn favorite-btn ${download.is_favorite ? 'active' : ''}"
                        data-id="${download.id}"
                        title="${download.is_favorite ? '取消收藏' : '收藏'}"
                        onclick="toggleFavorite(${download.id})">
                    <i class="fas fa-star"></i>
                </button>
                <button class="action-btn notes-btn ${download.notes ? 'has-notes' : ''}"
                        data-id="${download.id}"
                        title="${download.notes ? '编辑笔记' : '添加笔记'}"
                        onclick="showNotesModal(${download.id}, '${download.title || '未知标题'}')">
                    <i class="fas fa-sticky-note"></i>
                </button>
                <button class="action-btn delete-btn"
                        data-id="${download.id}"
                        title="删除"
                        onclick="deleteSearchRecord(${download.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        grid.appendChild(card);
    });

    container.appendChild(grid);

    // 添加复选框事件
    addCheckboxEvents();
}

// 添加复选框事件
function addCheckboxEvents() {
    const checkboxes = document.querySelectorAll('.download-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const downloadId = parseInt(checkbox.dataset.id);

            if (checkbox.checked) {
                if (!selectedDownloadIds.includes(downloadId)) {
                    selectedDownloadIds.push(downloadId);
                }
            } else {
                selectedDownloadIds = selectedDownloadIds.filter(id => id !== downloadId);

                // 取消全选复选框
                const selectAllCheckbox = document.getElementById('select-all-checkbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
            }

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        });
    });
}

// 更新批量操作按钮状态
function updateBatchActionButtons() {
    const batchActionButtons = document.querySelectorAll('.batch-action-btn');
    const hasSelected = selectedDownloadIds.length > 0;

    batchActionButtons.forEach(btn => {
        if (hasSelected) {
            btn.classList.remove('disabled');
        } else {
            btn.classList.add('disabled');
        }
    });

    // 更新选中数量显示
    const selectedCountEl = document.getElementById('selected-count');
    if (selectedCountEl) {
        selectedCountEl.textContent = selectedDownloadIds.length;
    }
}

// 渲染分页
function renderPagination(currentPage, totalPages, totalItems) {
    const paginationContainer = document.getElementById('downloads-pagination');
    if (!paginationContainer) return;

    // 清空分页
    paginationContainer.innerHTML = '';

    // 如果只有一页，不显示分页
    if (totalPages <= 1) return;

    // 创建分页
    const pagination = document.createElement('div');
    pagination.className = 'pagination';

    // 添加总记录数信息
    const info = document.createElement('div');
    info.className = 'pagination-info';
    info.textContent = `共 ${totalItems} 条记录，${totalPages} 页`;
    pagination.appendChild(info);

    // 添加分页链接
    const links = document.createElement('div');
    links.className = 'pagination-links';

    // 上一页
    const prevLink = document.createElement('a');
    prevLink.className = `page-link ${currentPage <= 1 ? 'disabled' : ''}`;
    prevLink.href = '#';
    prevLink.dataset.page = currentPage - 1;
    prevLink.innerHTML = '<i class="fas fa-chevron-left"></i>';
    links.appendChild(prevLink);

    // 页码
    const maxPages = 5; // 最多显示的页码数
    let startPage = Math.max(1, currentPage - Math.floor(maxPages / 2));
    let endPage = Math.min(totalPages, startPage + maxPages - 1);

    if (endPage - startPage + 1 < maxPages) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageLink = document.createElement('a');
        pageLink.className = `page-link ${i === currentPage ? 'active' : ''}`;
        pageLink.href = '#';
        pageLink.dataset.page = i;
        pageLink.textContent = i;
        links.appendChild(pageLink);
    }

    // 下一页
    const nextLink = document.createElement('a');
    nextLink.className = `page-link ${currentPage >= totalPages ? 'disabled' : ''}`;
    nextLink.href = '#';
    nextLink.dataset.page = currentPage + 1;
    nextLink.innerHTML = '<i class="fas fa-chevron-right"></i>';
    links.appendChild(nextLink);

    pagination.appendChild(links);
    paginationContainer.appendChild(pagination);
}

// 加载搜索统计
async function loadDownloadStats() {
    const statsContainer = document.getElementById('download-stats');
    if (!statsContainer) return;

    try {
        const response = await fetch('/api/search/stats');
        const data = await response.json();

        if (data.success) {
            const { total_searches, platform_stats, recent_searches, favorite_count, content_type_stats } = data.data;

            // 更新统计信息
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${total_searches}</div>
                    <div class="stat-label">总搜索数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${recent_searches}</div>
                    <div class="stat-label">最近7天</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${favorite_count || 0}</div>
                    <div class="stat-label">收藏</div>
                </div>
            `;

            // 添加平台统计
            if (platform_stats && Object.keys(platform_stats).length > 0) {
                const platformStatsContainer = document.createElement('div');
                platformStatsContainer.className = 'stats-section';
                platformStatsContainer.innerHTML = '<h4>平台分布</h4>';

                const platformStatsItems = document.createElement('div');
                platformStatsItems.className = 'stats-items';

                Object.entries(platform_stats).forEach(([platform, count]) => {
                    const platformItem = document.createElement('div');
                    platformItem.className = 'stat-item';
                    platformItem.innerHTML = `
                        <div class="stat-value">${count}</div>
                        <div class="stat-label">${getPlatformLabel(platform)}</div>
                    `;
                    platformStatsItems.appendChild(platformItem);
                });

                platformStatsContainer.appendChild(platformStatsItems);
                statsContainer.appendChild(platformStatsContainer);
            }

            // 添加内容类型统计
            if (content_type_stats && Object.keys(content_type_stats).length > 0) {
                const contentTypeStatsContainer = document.createElement('div');
                contentTypeStatsContainer.className = 'stats-section';
                contentTypeStatsContainer.innerHTML = '<h4>内容类型</h4>';

                const contentTypeStatsItems = document.createElement('div');
                contentTypeStatsItems.className = 'stats-items';

                Object.entries(content_type_stats).forEach(([contentType, count]) => {
                    const contentTypeItem = document.createElement('div');
                    contentTypeItem.className = 'stat-item';
                    contentTypeItem.innerHTML = `
                        <div class="stat-value">${count}</div>
                        <div class="stat-label">${getContentTypeLabel(contentType)}</div>
                    `;
                    contentTypeStatsItems.appendChild(contentTypeItem);
                });

                contentTypeStatsContainer.appendChild(contentTypeStatsItems);
                statsContainer.appendChild(contentTypeStatsContainer);
            }
        } else {
            console.error('获取搜索统计失败:', data.message);
        }
    } catch (error) {
        console.error('获取搜索统计请求失败:', error);
    }
}

// 获取平台标签
function getPlatformLabel(platform) {
    const platforms = {
        'kuaishou': '快手',
        'douyin': '抖音',
        'bilibili': '哔哩哔哩',
        'csdn': 'CSDN',
        'zhihu': '知乎',
        'weibo': '微博'
    };

    return platforms[platform] || platform;
}

// 获取内容类型标签
function getContentTypeLabel(contentType) {
    const contentTypes = {
        'video': '视频',
        'article': '文章',
        'resource': '资源',
        'image': '图片',
        'audio': '音频'
    };

    return contentTypes[contentType] || contentType;
}

// 获取内容类型图标
function getContentTypeIcon(contentType) {
    const contentTypeIcons = {
        'video': 'fas fa-video',
        'article': 'fas fa-file-alt',
        'resource': 'fas fa-file-archive',
        'image': 'fas fa-image',
        'audio': 'fas fa-music'
    };

    return contentTypeIcons[contentType] || 'fas fa-file';
}

// 获取状态标签
function getStatusLabel(status) {
    const statuses = {
        'success': '<span class="status-badge completed">成功</span>',
        'failed': '<span class="status-badge failed">失败</span>'
    };

    return statuses[status] || status;
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === undefined || bytes === null) return '-';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// 格式化时长
function formatDuration(seconds) {
    if (seconds === undefined || seconds === null) return '-';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 检查是否已存在Toast容器
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="fas ${getToastIcon(type)}"></i>
        </div>
        <div class="toast-message">${message}</div>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 显示Toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 300);
    }, 3000);
}

// 获取Toast图标
function getToastIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-times-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };

    return icons[type] || icons.info;
}

// 切换收藏状态
async function toggleFavorite(downloadId) {
    try {
        const response = await fetch(`/api/search/${downloadId}/favorite`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            // 更新UI
            const favoriteBtn = document.querySelector(`.favorite-btn[data-id="${downloadId}"]`);
            if (favoriteBtn) {
                if (data.data.is_favorite) {
                    favoriteBtn.classList.add('active');
                    favoriteBtn.title = '取消收藏';

                    // 添加收藏类名到行或卡片
                    const row = favoriteBtn.closest('tr');
                    const card = favoriteBtn.closest('.download-card');
                    if (row) row.classList.add('is-favorite');
                    if (card) card.classList.add('is-favorite');

                    showToast('已添加到收藏', 'success');
                } else {
                    favoriteBtn.classList.remove('active');
                    favoriteBtn.title = '收藏';

                    // 移除收藏类名
                    const row = favoriteBtn.closest('tr');
                    const card = favoriteBtn.closest('.download-card');
                    if (row) row.classList.remove('is-favorite');
                    if (card) card.classList.remove('is-favorite');

                    showToast('已取消收藏', 'info');
                }
            }

            // 如果当前是收藏视图，需要刷新列表
            if (currentFilters.is_favorite) {
                loadDownloadHistoryDebounced();
            } else {
                // 只更新统计信息，不重新加载列表
                loadDownloadStatsDebounced();
            }
        } else {
            showToast(`操作失败: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('切换收藏状态失败:', error);
        showToast('操作失败，请稍后再试', 'error');
    }
}

// 批量切换收藏状态
async function batchToggleFavorite(downloadIds, isFavorite) {
    if (!downloadIds.length) return;

    const promises = downloadIds.map(id =>
        fetch(`/api/search/${id}/favorite`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => response.json())
    );

    try {
        const results = await Promise.all(promises);
        const successCount = results.filter(result => result.success).length;

        if (successCount > 0) {
            showToast(`成功${isFavorite ? '添加' : '取消'}${successCount}个收藏`, 'success');

            // 刷新列表
            loadDownloadHistoryDebounced();
        } else {
            showToast('操作失败，请稍后再试', 'error');
        }
    } catch (error) {
        console.error('批量切换收藏状态失败:', error);
        showToast('操作失败，请稍后再试', 'error');
    }
}

// 删除搜索记录
async function deleteSearchRecord(downloadId) {
    if (!confirm('确定要删除此搜索记录吗？')) return;

    try {
        const response = await fetch(`/api/search/${downloadId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showToast('删除成功', 'success');

            // 刷新列表
            loadDownloadHistoryDebounced();
        } else {
            showToast(`删除失败: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('删除搜索记录失败:', error);
        showToast('删除失败，请稍后再试', 'error');
    }
}

// 批量删除搜索记录
async function batchDeleteDownloads(downloadIds) {
    if (!downloadIds.length) return;

    try {
        const response = await fetch('/api/search/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ record_ids: downloadIds })
        });

        const data = await response.json();

        if (data.success) {
            showToast(`成功删除${data.data.deleted_count}条记录`, 'success');

            // 刷新列表
            loadDownloadHistoryDebounced();
        } else {
            showToast(`删除失败: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('批量删除搜索记录失败:', error);
        showToast('删除失败，请稍后再试', 'error');
    }
}

// 显示笔记模态框
function showNotesModal(downloadId, title) {
    // 检查是否已存在笔记模态框
    let notesModal = document.getElementById('notes-modal');
    if (!notesModal) {
        // 创建模态框
        notesModal = document.createElement('div');
        notesModal.className = 'modal';
        notesModal.id = 'notes-modal';

        notesModal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h2>笔记</h2>
                    <button class="close-modal"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="notes-title"></div>
                    <form id="notes-form">
                        <input type="hidden" id="notes-download-id">
                        <div class="form-group">
                            <textarea id="notes-content" rows="6" placeholder="添加笔记..."></textarea>
                        </div>
                        <div class="status-container" id="notes-status" style="display: none;">
                            <p class="status-message"></p>
                        </div>
                        <button type="submit" class="submit-btn">保存</button>
                    </form>
                </div>
            </div>
        `;

        document.body.appendChild(notesModal);

        // 关闭按钮事件
        const closeBtn = notesModal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notesModal.classList.remove('active');
            });
        }

        // 表单提交事件
        const notesForm = document.getElementById('notes-form');
        if (notesForm) {
            notesForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const downloadId = document.getElementById('notes-download-id').value;
                const notes = document.getElementById('notes-content').value;

                await saveNotes(downloadId, notes);
            });
        }
    }

    // 更新模态框内容
    const titleEl = notesModal.querySelector('.notes-title');
    if (titleEl) {
        titleEl.textContent = title;
    }

    // 设置下载ID
    const downloadIdInput = document.getElementById('notes-download-id');
    if (downloadIdInput) {
        downloadIdInput.value = downloadId;
    }

    // 获取并显示笔记内容
    loadNotes(downloadId);

    // 显示模态框
    notesModal.classList.add('active');
}

// 加载笔记内容
async function loadNotes(downloadId) {
    try {
        // 获取搜索记录详情
        const response = await fetch(`/api/search/history?record_id=${downloadId}`);
        const data = await response.json();

        if (data.success && data.data.records && data.data.records.length > 0) {
            const record = data.data.records[0];

            // 更新笔记内容
            const notesContent = document.getElementById('notes-content');
            if (notesContent) {
                notesContent.value = record.notes || '';
            }
        } else {
            console.error('获取笔记内容失败:', data.message);
        }
    } catch (error) {
        console.error('加载笔记内容失败:', error);
    }
}

// 保存笔记
async function saveNotes(downloadId, notes) {
    const statusContainer = document.getElementById('notes-status');

    try {
        const response = await fetch(`/api/search/${downloadId}/notes`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes })
        });

        const data = await response.json();

        if (data.success) {
            // 显示成功消息
            if (statusContainer) {
                statusContainer.style.display = 'block';
                statusContainer.innerHTML = '<p class="status-message success">保存成功</p>';

                // 自动隐藏
                setTimeout(() => {
                    statusContainer.style.display = 'none';
                }, 2000);
            }

            // 更新UI
            const notesBtn = document.querySelector(`.notes-btn[data-id="${downloadId}"]`);
            if (notesBtn) {
                if (notes) {
                    notesBtn.classList.add('has-notes');
                    notesBtn.title = '编辑笔记';
                } else {
                    notesBtn.classList.remove('has-notes');
                    notesBtn.title = '添加笔记';
                }
            }

            // 关闭模态框
            setTimeout(() => {
                const notesModal = document.getElementById('notes-modal');
                if (notesModal) {
                    notesModal.classList.remove('active');
                }

                // 不需要刷新整个列表，笔记更新只影响UI显示
                // loadDownloadHistory(); // 移除这个调用以避免重复请求
            }, 1500);
        } else {
            // 显示错误消息
            if (statusContainer) {
                statusContainer.style.display = 'block';
                statusContainer.innerHTML = `<p class="status-message error">保存失败: ${data.message}</p>`;
            }
        }
    } catch (error) {
        console.error('保存笔记失败:', error);

        // 显示错误消息
        if (statusContainer) {
            statusContainer.style.display = 'block';
            statusContainer.innerHTML = '<p class="status-message error">保存失败，请稍后再试</p>';
        }
    }
}

// 导出搜索记录
function exportDownloads() {
    // 获取当前筛选条件下的所有搜索记录
    fetch(`/api/search/history?per_page=1000&${new URLSearchParams(currentFilters).toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.records) {
                const records = data.data.records;

                // 转换为CSV格式
                const headers = ['ID', '标题', '平台', '类型', '作者', '状态', '时间', '搜索次数', '收藏'];
                const rows = records.map(record => [
                    record.id,
                    record.title || '未知标题',
                    getPlatformLabel(record.platform),
                    getContentTypeLabel(record.content_type),
                    record.author || '未知作者',
                    record.status,
                    formatDate(record.created_at),
                    record.search_count || 0,
                    record.is_favorite ? '是' : '否'
                ]);

                // 添加表头
                rows.unshift(headers);

                // 转换为CSV字符串
                const csv = rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

                // 创建Blob对象
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

                // 创建下载链接
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `搜索记录_${new Date().toISOString().slice(0, 10)}.csv`;
                link.style.display = 'none';

                // 触发下载
                document.body.appendChild(link);
                link.click();

                // 清理
                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(link.href);
                }, 100);

                showToast('导出成功', 'success');
            } else {
                showToast('导出失败，请稍后再试', 'error');
            }
        })
        .catch(error => {
            console.error('导出搜索记录失败:', error);
            showToast('导出失败，请稍后再试', 'error');
        });
}

// 导出函数
window.initDownloadFeatures = initDownloadFeatures;
window.handleDownloadsLinkClick = handleDownloadsLinkClick;
window.initDownloadFeatures = initDownloadFeatures;
window.loadDownloadHistory = loadDownloadHistory;
window.toggleFavorite = toggleFavorite;
window.deleteSearchRecord = deleteSearchRecord;
window.showNotesModal = showNotesModal;

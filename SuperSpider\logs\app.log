2025-06-08 01:58:37,332 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 01:58:37,333 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 01:58:37,337 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 01:58:37,337 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 01:58:37,339 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 01:58:37,339 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 01:58:37,944 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 01:58:38,899 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 01:58:38,903 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 01:58:39,198 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 01:58:39,204 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 01:58:39,212 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 01:58:39,216 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 01:58:39,228 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 01:58:39,234 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 01:58:39,240 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 01:58:39,246 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 01:58:39,282 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 01:58:39,283 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 01:58:39,296 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 01:58:40,480 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 01:58:40,481 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 01:58:40,483 - werkzeug - INFO -  * Restarting with stat
2025-06-08 01:58:41,286 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 01:58:41,286 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 01:58:41,287 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 01:58:41,288 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 01:58:41,288 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 01:58:41,288 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 01:58:42,567 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 01:58:42,567 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 01:58:42,581 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 01:58:42,595 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 01:58:42,621 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 01:58:53,444 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "GET / HTTP/1.1" 200 -
2025-06-08 01:58:53,597 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,613 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,619 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,655 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,660 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,665 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,672 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,729 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,746 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,780 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:53,780 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-06-08 01:58:53,798 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:53] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 01:58:54,042 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:54] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 01:58:54,055 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:54] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 01:58:54,536 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 01:58:54,550 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:58:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 01:59:02,033 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:02] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 01:59:02,987 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 01:59:08,450 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:08] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-08 01:59:17,769 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:17] "POST /api/auth/check-username HTTP/1.1" 200 -
2025-06-08 01:59:17,951 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:17] "POST /api/auth/check-username HTTP/1.1" 200 -
2025-06-08 01:59:19,523 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:19] "POST /api/auth/check-username HTTP/1.1" 200 -
2025-06-08 01:59:22,608 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:22] "POST /api/auth/check-phone HTTP/1.1" 200 -
2025-06-08 01:59:22,750 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:22] "POST /api/auth/check-phone HTTP/1.1" 200 -
2025-06-08 01:59:23,860 - superspider.auth - INFO - 注册验证码发送到 17630568237: 885226
2025-06-08 01:59:23,862 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:23] "POST /api/auth/send-register-sms HTTP/1.1" 200 -
2025-06-08 01:59:35,660 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:35] "[35m[1mPOST /api/auth/register HTTP/1.1[0m" 201 -
2025-06-08 01:59:44,796 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-06-08 01:59:45,103 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-06-08 01:59:49,585 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-06-08 01:59:49,599 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:49] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-06-08 01:59:50,210 - backend.api.search_api - INFO - 用户 yumu001 创建搜索记录: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-06-08 01:59:50,217 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:50] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 01:59:54,528 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:54] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-06-08 01:59:54,543 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:54] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-06-08 01:59:54,573 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:54] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-08 01:59:54,598 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 01:59:54] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-08 02:00:02,941 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:00:04,260 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:00:04,261 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:04] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:00:04,794 - backend.api.search_api - INFO - 用户 yumu001 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:00:04,795 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:04] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:08,639 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:00:09,865 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:00:09,865 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:09] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:00:10,451 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:10] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:13,902 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:00:15,045 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 看一下#cos #鸣潮 #椿cos #漫展
2025-06-08 02:00:15,045 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:15] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:00:15,569 - backend.api.search_api - INFO - 用户 yumu001 创建搜索记录: 看一下#cos #鸣潮 #椿cos #漫展
2025-06-08 02:00:15,570 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:15] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:20,535 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:00:20,844 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:00:20,844 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:20] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:00:21,366 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:21] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:30,601 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-06-08 02:00:30,609 - backend.spiders.base_spider - INFO - 初始化爬虫: 哔哩哔哩爬虫
2025-06-08 02:00:31,176 - backend.spiders.bilibili_spider - INFO - 获取到最终URL: https://www.bilibili.com/video/BV1uzAZeVEYA/
2025-06-08 02:00:31,503 - backend.spiders.bilibili_spider - INFO - 视频文件已存在: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:00:31,504 - backend.spiders.bilibili_spider - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:00:31,505 - backend.api.bilibili_api - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:00:31,506 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:31] "POST /api/bilibili/parse HTTP/1.1" 200 -
2025-06-08 02:00:31,517 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:31] "[33mGET /u002F/u002Fi2.hdslb.com/u002Fbfs/u002Farchive/u002Fc16e09fb1a1a2d86149343e9df435132701cf836.jpg HTTP/1.1[0m" 404 -
2025-06-08 02:00:31,526 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:31] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-06-08 02:00:31,562 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:31] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-06-08 02:00:32,031 - backend.api.search_api - INFO - 用户 yumu001 创建搜索记录: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:00:32,032 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:32] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:39,769 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "GET / HTTP/1.1" 200 -
2025-06-08 02:00:39,796 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,814 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,821 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,829 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,845 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,858 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,890 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,907 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,923 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,926 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,934 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:39,938 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:39] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:00:40,008 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:00:40,021 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:00:40,484 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:00:40,490 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:00:41,769 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:41] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-06-08 02:00:41,797 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:41] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-06-08 02:00:41,824 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:41] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-08 02:00:41,887 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:41] "GET /api/search/stats HTTP/1.1" 200 -
2025-06-08 02:00:44,943 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-06-08 02:00:45,226 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-06-08 02:00:48,318 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-08 02:00:48,321 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:48] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-06-08 02:00:48,922 - backend.api.search_api - INFO - 用户 yumu001 创建搜索记录: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-08 02:00:48,933 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:48] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:53,386 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:00:53,614 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:00:53,616 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:53] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:00:54,179 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:54] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:00:57,005 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:00:57,326 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 看一下#cos #鸣潮 #椿cos #漫展
2025-06-08 02:00:57,326 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:57] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:00:57,863 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:00:57] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:01:00,531 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:01:00,776 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:01:00,777 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:00] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:01:01,296 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:01] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:01:07,499 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-06-08 02:01:07,507 - backend.spiders.bilibili_spider - INFO - 获取到最终URL: https://www.bilibili.com/video/BV1uzAZeVEYA/
2025-06-08 02:01:07,862 - backend.spiders.bilibili_spider - INFO - 视频文件已存在: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:01:07,862 - backend.spiders.bilibili_spider - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:01:07,862 - backend.api.bilibili_api - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-06-08 02:01:07,863 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:07] "POST /api/bilibili/parse HTTP/1.1" 200 -
2025-06-08 02:01:07,877 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:07] "[33mGET /u002F/u002Fi2.hdslb.com/u002Fbfs/u002Farchive/u002Fc16e09fb1a1a2d86149343e9df435132701cf836.jpg HTTP/1.1[0m" 404 -
2025-06-08 02:01:07,880 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:07] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-06-08 02:01:07,899 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:07] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-06-08 02:01:07,916 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:07] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-06-08 02:01:07,960 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:07] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-06-08 02:01:08,381 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:01:08] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:03:46,692 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:03:46] "GET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1" 200 -
2025-06-08 02:03:54,827 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:03:55,085 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:03:55,086 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:03:55] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:03:55,625 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:03:55] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:04:06,098 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:04:06,336 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 看一下#cos #鸣潮 #椿cos #漫展
2025-06-08 02:04:06,337 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:06] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:04:06,876 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:06] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:04:12,635 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:04:12,872 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:04:12,872 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:12] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:04:13,396 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:13] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:04:25,108 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-06-08 02:04:25,109 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-06-08 02:04:29,545 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-08 02:04:29,747 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:29] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-06-08 02:04:30,314 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:30] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:04:32,386 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:04:32] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:05:41,494 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:05:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:06:19,041 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\douyin_api.py', reloading
2025-06-08 02:06:19,888 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:06:21,905 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:06:21,906 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:06:21,906 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:06:21,907 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:06:21,907 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:06:21,907 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:06:24,131 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:06:24,131 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:06:24,140 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:06:24,174 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:06:24,191 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:06:32,367 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\douyin_api.py', reloading
2025-06-08 02:06:32,613 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:06:34,200 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:06:34,201 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:06:34,202 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:06:34,202 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:06:34,203 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:06:34,203 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:06:35,639 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:06:35,643 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:06:35,650 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:06:35,681 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:06:35,710 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:06:45,939 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\douyin_api.py', reloading
2025-06-08 02:06:46,255 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:06:47,366 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:06:47,367 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:06:47,370 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:06:47,370 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:06:47,371 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:06:47,371 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:06:48,961 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:06:48,962 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:06:48,974 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:06:49,014 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:06:49,039 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:07:06,427 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\kuaishou_api.py', reloading
2025-06-08 02:07:06,680 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:07:07,854 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:07:07,854 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:07:07,856 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:07:07,856 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:07:07,857 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:07:07,857 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:07:09,147 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:07:09,147 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:07:09,157 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:07:09,172 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:07:09,192 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:07:20,414 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\kuaishou_api.py', reloading
2025-06-08 02:07:21,093 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:07:22,062 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:07:22,063 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:07:22,064 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:07:22,064 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:07:22,064 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:07:22,064 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:07:23,649 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:07:23,655 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:07:23,665 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:07:23,699 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:07:23,725 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:07:30,901 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\kuaishou_api.py', reloading
2025-06-08 02:07:31,185 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:07:32,084 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:07:32,085 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:07:32,086 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:07:32,086 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:07:32,087 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:07:32,087 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:07:33,441 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:07:33,442 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:07:33,456 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:07:33,469 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:07:33,485 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:07:53,980 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\bilibili_api.py', reloading
2025-06-08 02:07:54,293 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:07:55,448 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:07:55,449 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:07:55,454 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:07:55,454 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:07:55,455 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:07:55,455 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:07:56,869 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:07:56,869 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:07:56,879 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:07:56,894 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:07:56,911 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:08:05,097 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\bilibili_api.py', reloading
2025-06-08 02:08:05,355 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:08:06,623 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:08:06,623 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:08:06,624 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:08:06,624 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:08:06,624 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:08:06,624 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:08:08,153 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:08:08,153 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:08:08,164 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:08:08,183 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:08:08,204 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:10:39,878 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:10:39,878 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:10:39,879 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:10:39,879 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:10:39,880 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:10:39,880 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:10:40,340 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 02:10:40,940 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 02:10:40,944 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 02:10:41,207 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 02:10:41,226 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 02:10:41,253 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 02:10:41,274 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 02:10:41,328 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 02:10:41,345 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 02:10:41,361 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 02:10:41,365 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 02:10:41,439 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:10:41,443 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:10:41,458 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:10:42,632 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 02:10:42,636 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 02:10:42,640 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:10:43,831 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:10:43,833 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:10:43,837 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:10:43,837 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:10:43,840 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:10:43,840 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:10:45,163 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:10:45,172 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:10:45,185 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:10:45,229 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:10:45,268 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:10:45,416 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:10:45,443 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "GET / HTTP/1.1" 200 -
2025-06-08 02:10:45,473 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,477 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,488 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,490 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,492 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,530 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,533 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,552 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,557 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,613 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,644 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,646 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:10:45,673 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:10:45,682 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:10:46,173 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:10:46,183 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:10:48,327 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-06-08 02:10:48,758 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-06-08 02:10:52,635 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-08 02:10:52,765 - superspider.permissions - INFO - 记录API调用 - 用户: yumu001, 今日第1次
2025-06-08 02:10:52,857 - superspider.permissions - INFO - 记录下载操作 - 用户: yumu001, 今日第1次
2025-06-08 02:10:52,857 - superspider.permissions - INFO - 用户 yumu001 执行搜索操作成功
2025-06-08 02:10:52,858 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:52] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-06-08 02:10:53,434 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:53] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:10:56,209 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:10:57,201 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:10:57,208 - superspider.permissions - INFO - 记录API调用 - 用户: yumu001, 今日第2次
2025-06-08 02:10:57,220 - superspider.permissions - INFO - 记录下载操作 - 用户: yumu001, 今日第2次
2025-06-08 02:10:57,221 - superspider.permissions - INFO - 用户 yumu001 执行搜索操作成功
2025-06-08 02:10:57,223 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:57] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:10:57,764 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:10:57] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:10:59,806 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:11:00,761 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 看一下#cos #鸣潮 #椿cos #漫展
2025-06-08 02:11:00,770 - superspider.permissions - INFO - 记录API调用 - 用户: yumu001, 今日第3次
2025-06-08 02:11:00,781 - superspider.permissions - INFO - 记录下载操作 - 用户: yumu001, 今日第3次
2025-06-08 02:11:00,782 - superspider.permissions - INFO - 用户 yumu001 执行搜索操作成功
2025-06-08 02:11:00,783 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:00] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:11:01,304 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:01] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:11:03,801 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:11:04,726 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:11:04,733 - superspider.permissions - INFO - 记录API调用 - 用户: yumu001, 今日第4次
2025-06-08 02:11:04,743 - superspider.permissions - INFO - 记录下载操作 - 用户: yumu001, 今日第4次
2025-06-08 02:11:04,743 - superspider.permissions - INFO - 用户 yumu001 执行搜索操作成功
2025-06-08 02:11:04,745 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:04] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:11:05,269 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:05] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:11:07,618 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-06-08 02:11:07,862 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-06-08 02:11:07,869 - superspider.permissions - INFO - 记录API调用 - 用户: yumu001, 今日第5次
2025-06-08 02:11:07,884 - superspider.permissions - INFO - 记录下载操作 - 用户: yumu001, 今日第5次
2025-06-08 02:11:07,884 - superspider.permissions - INFO - 用户 yumu001 执行搜索操作成功
2025-06-08 02:11:07,885 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:07] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-06-08 02:11:08,415 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:08] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 02:11:11,769 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:11:11] "[31m[1mPOST /api/bilibili/parse HTTP/1.1[0m" 429 -
2025-06-08 02:12:40,081 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 02:12:40,387 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:12:41,723 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:12:41,731 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:12:41,732 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:12:41,732 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:12:41,733 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:12:41,733 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:12:43,687 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:12:43,688 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:12:43,699 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:12:43,718 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:12:43,737 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:15:46,634 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:15:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:20:46,406 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:20:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:25:46,423 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:25:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:30:34,598 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:34] "[31m[1mPOST /api/bilibili/parse HTTP/1.1[0m" 429 -
2025-06-08 02:30:36,714 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "GET / HTTP/1.1" 200 -
2025-06-08 02:30:36,789 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,791 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,798 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,806 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,808 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,830 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,831 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-08 02:30:36,832 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,839 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,852 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,853 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,855 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:30:36,865 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "GET /static/js/limit-handler.js HTTP/1.1" 200 -
2025-06-08 02:30:36,953 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:30:36,969 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:36] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:30:37,451 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:30:37,465 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:30:40,138 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:40] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:30:49,312 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:30:49] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:31:24,550 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:31:24,551 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:31:24,558 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:31:24,558 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:31:24,559 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:31:24,559 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:31:25,026 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 02:31:25,906 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 02:31:25,911 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 02:31:26,166 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 02:31:26,175 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 02:31:26,180 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 02:31:26,183 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 02:31:26,192 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 02:31:26,195 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 02:31:26,199 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 02:31:26,204 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 02:31:26,234 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:31:26,235 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:31:26,255 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:31:26,307 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 02:31:26,308 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 02:31:26,310 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:31:27,224 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:31:27,225 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:31:27,226 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:31:27,226 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:31:27,227 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:31:27,227 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:31:28,939 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:31:28,945 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:31:28,964 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:31:28,998 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:31:29,062 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:31:31,364 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:31] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:31:33,324 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "GET / HTTP/1.1" 200 -
2025-06-08 02:31:33,369 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,385 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,389 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,393 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,394 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,407 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,422 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,427 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,438 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,458 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,479 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,513 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,543 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 02:31:33,591 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:31:33,605 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:31:34,058 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:31:34,069 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:31:37,341 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:31:37] "[31m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 429 -
2025-06-08 02:35:24,532 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:35:24] "[31m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 429 -
2025-06-08 02:35:37,876 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:35:37] "[31m[1mPOST /api/kuaishou/parse HTTP/1.1[0m" 429 -
2025-06-08 02:36:34,390 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:36:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:36:53,469 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 02:36:54,004 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:36:55,890 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:36:55,890 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:36:55,892 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:36:55,892 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:36:55,892 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:36:55,892 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:36:58,240 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:36:58,327 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:36:58,350 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:36:58,397 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:36:58,430 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:37:36,812 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 02:37:37,282 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:37:38,579 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:37:38,580 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:37:38,593 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:37:38,593 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:37:38,594 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:37:38,594 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:37:40,852 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:37:40,858 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:37:40,875 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:37:40,909 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:37:40,929 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:38:53,436 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:38:53,437 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:38:53,438 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:38:53,438 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:38:53,438 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:38:53,438 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:38:54,045 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 02:38:54,659 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 02:38:54,665 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 02:38:54,925 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 02:38:54,938 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 02:38:54,944 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 02:38:54,952 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 02:38:54,957 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 02:38:54,959 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 02:38:54,971 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 02:38:54,975 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 02:44:00,399 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:44:00,400 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:44:00,401 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:44:00,401 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:44:00,402 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:44:00,402 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:44:00,976 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 02:44:02,174 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 02:44:02,181 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 02:44:02,474 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 02:44:02,482 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 02:44:02,486 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 02:44:02,489 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 02:44:02,491 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 02:44:02,497 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 02:44:02,504 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 02:44:02,507 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 02:44:02,551 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:44:02,554 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:44:02,567 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:44:03,739 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 02:44:03,739 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 02:44:03,743 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:44:05,622 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:44:05,622 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:44:05,624 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:44:05,624 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:44:05,625 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:44:05,625 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:44:07,186 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:44:07,187 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:44:07,200 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:44:07,219 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:44:07,235 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:44:07,306 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "GET / HTTP/1.1" 200 -
2025-06-08 02:44:07,353 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,379 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,388 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,398 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,405 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,413 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,422 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,440 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,445 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,499 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,500 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,513 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 02:44:07,548 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:44:07,563 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:44:08,053 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:44:08,066 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:44:10,721 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:10] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:44:32,075 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:32] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:44:37,390 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:44:37] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:46:07,754 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 02:46:08,296 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:46:09,746 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:46:09,772 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:46:09,812 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:46:09,812 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:46:09,813 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:46:09,813 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:46:11,591 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:46:11,592 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:46:11,607 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:46:11,639 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:46:11,657 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:47:00,922 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\permissions.py', reloading
2025-06-08 02:47:01,197 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:47:02,218 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:47:02,218 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:47:02,219 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:47:02,219 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:47:02,219 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:47:02,219 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:47:03,412 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:47:03,413 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:47:03,427 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:47:03,443 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:47:03,460 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:47:30,239 - superspider.permissions - INFO - 🔍 开始检查用户 yumu001 的搜索限制
2025-06-08 02:47:30,240 - superspider.permissions - INFO - 🔍 检查用户 yumu001 的 download 限制
2025-06-08 02:47:30,245 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=5, api_calls_this_minute=3
2025-06-08 02:47:30,261 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 02:47:30,266 - superspider.permissions - WARNING - 🚫 用户 yumu001 下载次数已达上限: 5/5
2025-06-08 02:47:30,267 - superspider.permissions - INFO - 📥 每日下载检查: can_download=False, remaining=0, error=今日下载次数已达上限(5次)，请明天再试或升级账户
2025-06-08 02:47:30,267 - superspider.permissions - WARNING - 🚫 用户 yumu001 达到每日下载限制: 5次
2025-06-08 02:47:30,269 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:47:30] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:49:35,772 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:49:35,772 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:49:35,773 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:49:35,773 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:49:35,773 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:49:35,773 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:49:36,236 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 02:49:36,921 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 02:49:36,926 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 02:49:37,185 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 02:49:37,191 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 02:49:37,196 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 02:49:37,202 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 02:49:37,207 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 02:49:37,209 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 02:49:37,217 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 02:49:37,221 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 02:51:24,619 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:51:24,619 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:51:24,620 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:51:24,620 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:51:24,621 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:51:24,621 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:51:25,149 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 02:51:25,786 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 02:51:25,790 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 02:51:26,053 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 02:51:26,064 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 02:51:26,070 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 02:51:26,073 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 02:51:26,078 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 02:51:26,083 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 02:51:26,088 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 02:51:26,091 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 02:51:26,127 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:51:26,129 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:51:26,151 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:51:28,344 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 02:51:28,345 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 02:51:28,348 - werkzeug - INFO -  * Restarting with stat
2025-06-08 02:51:29,200 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 02:51:29,200 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 02:51:29,201 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 02:51:29,201 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 02:51:29,201 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 02:51:29,202 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 02:51:30,374 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 02:51:30,376 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 02:51:30,386 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 02:51:30,411 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 02:51:30,447 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 02:51:34,526 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "GET / HTTP/1.1" 200 -
2025-06-08 02:51:34,591 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,597 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,600 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,618 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,618 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,640 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,660 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,665 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,673 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,676 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,682 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,696 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,699 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 02:51:34,773 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:51:34,783 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:34] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:51:35,281 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:51:35,286 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:51:37,751 - superspider.permissions - INFO - 🔍 开始检查用户 yumu001 的搜索限制
2025-06-08 02:51:37,752 - superspider.permissions - INFO - 🔍 检查用户 yumu001 的 download 限制
2025-06-08 02:51:37,757 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=5, api_calls_this_minute=3
2025-06-08 02:51:37,759 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 02:51:37,760 - superspider.permissions - WARNING - 🚫 用户 yumu001 下载次数已达上限: 5/5
2025-06-08 02:51:37,762 - superspider.permissions - INFO - 📥 每日下载检查: can_download=False, remaining=0, error=今日下载次数已达上限(5次)，请明天再试或升级账户
2025-06-08 02:51:37,762 - superspider.permissions - WARNING - 🚫 用户 yumu001 达到每日下载限制: 5次
2025-06-08 02:51:37,764 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:51:37] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:54:36,703 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:36] "GET / HTTP/1.1" 200 -
2025-06-08 02:54:36,924 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:36] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,004 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,074 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,091 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,129 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,249 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,378 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,409 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,425 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,470 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,480 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,490 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-08 02:54:37,524 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 02:54:37,836 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:54:37,927 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:37] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 02:54:38,342 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:38] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:54:38,350 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:38] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 02:54:48,120 - superspider.permissions - INFO - 🔍 开始检查用户 yumu001 的搜索限制
2025-06-08 02:54:48,121 - superspider.permissions - INFO - 🔍 检查用户 yumu001 的 download 限制
2025-06-08 02:54:48,122 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=5, api_calls_this_minute=3
2025-06-08 02:54:48,123 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 02:54:48,123 - superspider.permissions - WARNING - 🚫 用户 yumu001 下载次数已达上限: 5/5
2025-06-08 02:54:48,123 - superspider.permissions - INFO - 📥 每日下载检查: can_download=False, remaining=0, error=今日下载次数已达上限(5次)，请明天再试或升级账户
2025-06-08 02:54:48,124 - superspider.permissions - WARNING - 🚫 用户 yumu001 达到每日下载限制: 5次
2025-06-08 02:54:48,125 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:54:48] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:55:02,105 - superspider.permissions - INFO - 🔍 开始检查用户 yumu001 的搜索限制
2025-06-08 02:55:02,105 - superspider.permissions - INFO - 🔍 检查用户 yumu001 的 download 限制
2025-06-08 02:55:02,108 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=5, api_calls_this_minute=3
2025-06-08 02:55:02,108 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 02:55:02,109 - superspider.permissions - WARNING - 🚫 用户 yumu001 下载次数已达上限: 5/5
2025-06-08 02:55:02,110 - superspider.permissions - INFO - 📥 每日下载检查: can_download=False, remaining=0, error=今日下载次数已达上限(5次)，请明天再试或升级账户
2025-06-08 02:55:02,110 - superspider.permissions - WARNING - 🚫 用户 yumu001 达到每日下载限制: 5次
2025-06-08 02:55:02,111 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:55:02] "[31m[1mPOST /api/douyin/parse HTTP/1.1[0m" 429 -
2025-06-08 02:59:38,662 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 02:59:38] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:07:27,260 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:07:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:09:38,406 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:09:38] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:13:29,928 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:29] "GET / HTTP/1.1" 200 -
2025-06-08 03:13:30,045 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-08 03:13:30,301 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 03:13:30,418 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 03:13:30,509 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 03:13:30,531 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:30,784 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:30,819 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:30,874 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:30] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:31,018 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:31,069 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:31,103 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:31,138 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:31,142 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 03:13:31,217 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 03:13:31,236 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 03:13:31,758 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:13:31,847 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:13:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:18:32,447 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:18:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:21:45,811 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "GET / HTTP/1.1" 200 -
2025-06-08 03:21:45,859 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 03:21:45,945 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-08 03:21:45,951 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 03:21:45,951 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 03:21:45,952 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:45,958 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,155 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,406 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,465 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,514 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,526 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,544 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:46,601 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:46] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 03:21:47,001 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 03:21:47,022 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 03:21:47,434 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:21:47,441 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:21:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:23:32,396 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:23:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:26:48,399 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:26:48] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:27:31,171 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 03:27:31,172 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 03:27:31,177 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 03:27:31,177 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 03:27:31,178 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 03:27:31,178 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 03:27:31,907 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 03:27:33,507 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 03:27:33,518 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 03:27:33,981 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 03:27:33,992 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 03:27:33,997 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 03:27:34,000 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 03:27:34,003 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 03:27:34,011 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 03:27:34,021 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 03:27:34,028 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 03:27:34,063 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 03:27:34,063 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 03:27:34,082 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 03:27:35,282 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 03:27:35,283 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 03:27:35,285 - werkzeug - INFO -  * Restarting with stat
2025-06-08 03:27:36,448 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 03:27:36,448 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 03:27:36,449 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 03:27:36,449 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 03:27:36,449 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 03:27:36,449 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 03:27:37,789 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 03:27:37,790 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 03:27:37,800 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 03:27:37,830 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 03:27:37,863 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 03:27:44,027 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "GET / HTTP/1.1" 200 -
2025-06-08 03:27:44,080 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,102 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,131 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,143 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,209 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,238 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,252 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,253 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,335 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,380 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,381 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,382 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:44,425 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:44] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 03:27:45,008 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 03:27:45,087 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 03:27:45,446 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:27:45,465 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:27:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:32:40,327 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-06-08 03:32:41,862 - werkzeug - INFO -  * Restarting with stat
2025-06-08 03:32:43,090 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 03:32:43,090 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 03:32:43,091 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 03:32:43,091 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 03:32:43,092 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 03:32:43,092 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 03:32:44,491 - superspider - ERROR - 注册证件照API路由失败: cannot import name 'check_user_permissions' from 'backend.utils.permissions' (D:\Program Files\VsCodeProject\SuperSpider\backend\utils\permissions.py)
2025-06-08 03:32:44,530 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 03:32:44,543 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 03:32:44,558 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 03:32:44,624 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 03:32:44,661 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 03:32:46,410 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:32:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:37:46,484 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:37:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:42:46,384 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 03:42:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 03:51:30,025 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 03:51:30,025 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 03:51:30,026 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 03:51:30,026 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 03:51:30,026 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 03:51:30,026 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 03:51:30,491 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 03:51:31,212 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 03:51:31,219 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 03:51:31,489 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 03:51:31,493 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 03:51:31,499 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 03:51:31,501 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 03:51:31,504 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 03:51:31,507 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 03:51:31,509 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 03:51:31,514 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 03:51:31,515 - superspider - ERROR - 注册证件照API路由失败: cannot import name 'check_user_permissions' from 'backend.utils.permissions' (D:\Program Files\VsCodeProject\SuperSpider\backend\utils\permissions.py)
2025-06-08 04:02:29,490 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:02:29,584 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:02:29,597 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:02:29,597 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:02:29,669 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:02:29,728 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:02:31,743 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 04:02:38,627 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 04:02:38,663 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 04:02:40,210 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 04:02:40,226 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 04:02:40,239 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 04:02:40,245 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 04:02:40,293 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 04:02:40,297 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 04:02:40,308 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 04:02:40,314 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 04:02:40,377 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 04:02:40,645 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:02:40,696 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:02:40,757 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:02:42,111 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 04:02:42,123 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 04:02:42,126 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:02:44,207 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:02:44,208 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:02:44,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:02:44,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:02:44,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:02:44,211 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:02:46,073 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:02:46,074 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:02:46,086 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:02:46,116 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:02:46,142 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:14:59,810 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:14:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:15:00,098 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:15:43,852 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:43] "GET / HTTP/1.1" 200 -
2025-06-08 04:15:44,170 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,259 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,359 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,450 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,519 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,652 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,724 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,736 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,766 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,902 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,946 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:44,999 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:44] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:45,022 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:45] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:15:45,108 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:15:45,280 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:15:45,648 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:15:45,838 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:15:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:21:26,466 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:21:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:22:17,666 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:22:19,444 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:22:22,488 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:22:22,491 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:22:22,521 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:22:22,526 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:22:22,544 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:22:22,545 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:22:25,755 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:22:25,756 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:22:25,783 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:22:25,827 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:22:25,887 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:25:46,956 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:25:47,893 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:47] "GET / HTTP/1.1" 200 -
2025-06-08 04:25:47,992 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:25:47,995 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:47] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,002 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,008 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,009 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,052 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,052 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,107 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,110 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,112 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,113 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,120 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,124 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:25:48,316 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:25:48,342 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:25:48,825 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:25:48,838 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:25:48] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:30:40,018 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:30:41,613 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:30:43,482 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:30:43,482 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:30:43,487 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:30:43,487 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:30:43,487 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:30:43,488 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:30:46,086 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:30:46,091 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:30:46,107 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:30:46,181 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:30:46,229 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:31:37,258 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:31:37,981 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:31:39,923 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:31:39,925 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:31:39,926 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:31:39,926 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:31:39,926 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:31:39,927 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:31:42,429 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:31:42,431 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:31:42,451 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:31:42,521 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:31:42,550 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:33:58,133 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:33:58,984 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:58] "GET / HTTP/1.1" 200 -
2025-06-08 04:33:59,040 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,045 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,049 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,052 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,062 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,090 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,101 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,109 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,111 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,131 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,146 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:33:59,278 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:33:59,298 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:33:59,732 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:33:59,746 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:33:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:34:33,603 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "GET / HTTP/1.1" 200 -
2025-06-08 04:34:33,632 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,647 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,651 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,661 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,683 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,708 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,786 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,796 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,798 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,829 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,835 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,842 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,851 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:34:33,898 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:34:33,927 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:34:34,378 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:34:34,383 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:34:34] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:37:42,395 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:37:42] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 04:38:10,426 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:38:11,768 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:38:13,187 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:38:13,187 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:38:13,189 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:38:13,189 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:38:13,189 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:38:13,190 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:38:15,340 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:38:15,340 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:38:15,358 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:38:15,392 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:38:15,420 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:38:29,399 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:38:29,826 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:38:30,969 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:38:30,969 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:38:30,973 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:38:30,974 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:38:30,975 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:38:30,975 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:38:32,492 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:38:32,492 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:38:32,500 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:38:32,523 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:38:32,544 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:38:46,389 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:38:46,899 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:38:47,909 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:38:47,909 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:38:47,910 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:38:47,910 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:38:47,910 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:38:47,910 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:38:49,240 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:38:49,242 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:38:49,251 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:38:49,271 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:38:49,287 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:39:00,995 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:39:01,236 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:39:02,332 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:39:02,332 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:39:02,333 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:39:02,333 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:39:02,333 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:39:02,334 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:39:04,028 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:39:04,032 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:39:04,048 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:39:04,072 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:39:04,094 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:39:35,387 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:39:38,818 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:38] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 04:39:43,891 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:43] "GET / HTTP/1.1" 200 -
2025-06-08 04:39:43,980 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:43] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,026 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,256 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,343 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,413 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,427 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,429 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,443 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,525 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,547 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,563 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,563 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:39:44,597 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:39:44,624 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:39:45,248 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:39:45,359 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:39:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:40:26,179 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "GET / HTTP/1.1" 200 -
2025-06-08 04:40:26,375 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,377 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,407 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,407 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,441 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,475 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,512 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,610 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,640 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,693 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,890 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,895 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:26,942 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:26] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:40:27,021 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:27] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:40:27,090 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:27] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:40:27,490 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:40:27,501 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:40:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:41:39,149 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "GET / HTTP/1.1" 200 -
2025-06-08 04:41:39,172 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,176 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,179 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,188 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,192 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,215 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,216 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,231 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,239 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,241 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,249 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,259 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,259 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:41:39,273 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:41:39,322 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:41:39,780 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:41:39,787 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:41:39] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:45:50,129 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:45:50,901 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:45:52,563 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:45:52,564 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:45:52,565 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:45:52,565 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:45:52,565 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:45:52,565 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:45:54,953 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:45:54,958 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:45:54,978 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:45:55,040 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:45:55,179 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:46:05,784 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:46:06,073 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:46:07,658 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:46:07,660 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:46:07,671 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:46:07,671 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:46:07,672 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:46:07,673 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:46:09,669 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:46:09,670 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:46:09,687 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:46:09,740 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:46:09,764 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:46:20,382 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 04:46:20,784 - werkzeug - INFO -  * Restarting with stat
2025-06-08 04:46:21,839 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 04:46:21,840 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 04:46:21,841 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 04:46:21,841 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 04:46:21,841 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 04:46:21,841 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 04:46:23,447 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 04:46:23,447 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 04:46:23,461 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 04:46:23,482 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 04:46:23,498 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 04:54:32,702 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:32] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 04:54:32,717 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:32] "GET /api/idphoto/sizes HTTP/1.1" 200 -
2025-06-08 04:54:57,874 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:57] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:54:58,560 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "GET / HTTP/1.1" 200 -
2025-06-08 04:54:58,690 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,702 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,704 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,714 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,729 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,765 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,771 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,807 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,808 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,809 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,809 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,810 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:58,810 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 04:54:59,096 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:54:59,113 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 04:54:59,594 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 04:54:59,609 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 04:54:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 05:00:00,430 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 05:00:56,416 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "GET / HTTP/1.1" 200 -
2025-06-08 05:00:56,466 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,466 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,467 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,471 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,480 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,502 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,505 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,506 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,526 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,531 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,532 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,546 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,569 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 05:00:56,609 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 05:00:56,618 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:56] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 05:00:57,153 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:57] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 05:00:57,180 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 05:00:57] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 10:02:42,375 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 10:02:42,734 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 10:46:23,624 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 10:46:23,802 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 15:54:31,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:54:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 15:54:31,652 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:54:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 15:56:15,670 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "GET / HTTP/1.1" 200 -
2025-06-08 15:56:15,736 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 15:56:15,741 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 15:56:15,751 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:15,752 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 15:56:15,837 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:15,837 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 15:56:15,855 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:15] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,260 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,332 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,341 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,473 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,506 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,518 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 15:56:16,541 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 15:56:16,570 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:16] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 15:56:17,053 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 15:56:17,070 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:56:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 15:58:53,361 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "GET / HTTP/1.1" 200 -
2025-06-08 15:58:53,400 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,421 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,443 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,450 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,454 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,476 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,481 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,553 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,557 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,579 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,580 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,596 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,598 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 15:58:53,654 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 15:58:53,662 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:53] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 15:58:54,131 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 15:58:54,144 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 15:58:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:00:33,431 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:00:33] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 16:02:42,864 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:02:43,016 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:04:57,109 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\test_model_call.py', reloading
2025-06-08 16:04:57,995 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:04:59,286 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:04:59,287 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:04:59,289 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:04:59,289 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:04:59,289 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:04:59,289 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:05:01,663 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:05:01,664 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:05:01,682 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:05:01,732 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:05:01,752 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:05:19,612 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\test_model_call.py', reloading
2025-06-08 16:05:19,816 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:05:20,666 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:05:20,666 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:05:20,667 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:05:20,667 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:05:20,668 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:05:20,668 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:05:21,827 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:05:21,828 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:05:21,838 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:05:21,853 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:05:21,868 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:05:32,275 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\test_model_call.py', reloading
2025-06-08 16:05:32,498 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:05:33,517 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:05:33,520 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:05:33,521 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:05:33,521 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:05:33,522 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:05:33,522 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:05:34,748 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:05:34,748 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:05:34,762 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:05:34,778 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:05:34,792 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:07:58,421 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:07:59,485 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "GET / HTTP/1.1" 200 -
2025-06-08 16:07:59,518 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,520 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,521 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,553 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,554 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,558 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,561 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,586 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,589 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,599 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,603 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,613 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,630 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:07:59,678 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:07:59,690 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:07:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:08:00,177 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:08:00,191 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:08:59,523 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "GET / HTTP/1.1" 200 -
2025-06-08 16:08:59,546 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,559 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,571 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,579 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,580 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,590 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,605 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,609 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,614 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,615 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,622 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,625 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:08:59,655 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:08:59,663 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:08:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:09:00,138 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:09:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:09:00,146 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:09:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:10:18,321 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:18] "GET / HTTP/1.1" 200 -
2025-06-08 16:10:19,124 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,156 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,168 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,185 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,200 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,218 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,222 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,233 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,234 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,238 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,250 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,257 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,272 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:10:19,291 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:10:19,308 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:10:19,799 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:10:19,813 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:10:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:12:22,056 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:12:22] "[32mPOST /api/idphoto/create HTTP/1.1[0m" 302 -
2025-06-08 16:12:22,069 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:12:22] "[31m[1mGET /api/auth/login?next=/api/idphoto/create HTTP/1.1[0m" 405 -
2025-06-08 16:12:22,086 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:12:22] "[32mPOST /api/idphoto/create HTTP/1.1[0m" 302 -
2025-06-08 16:12:22,092 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:12:22] "[31m[1mGET /api/auth/login?next=/api/idphoto/create HTTP/1.1[0m" 405 -
2025-06-08 16:26:20,065 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:26:20,066 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:26:20,070 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:26:20,070 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:26:20,070 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:26:20,070 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:26:20,573 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 16:26:21,258 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 16:26:21,262 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 16:26:21,510 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 16:26:21,516 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 16:26:21,523 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 16:26:21,527 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 16:26:21,530 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 16:26:21,533 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 16:26:21,541 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 16:26:21,545 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 16:26:21,549 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 16:26:21,599 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:26:21,599 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:26:21,619 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:26:21,656 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 16:26:21,656 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 16:26:21,659 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:26:22,471 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:26:22,472 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:26:22,473 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:26:22,473 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:26:22,474 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:26:22,474 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:26:23,728 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:26:23,729 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:26:23,742 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:26:23,755 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:26:23,775 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:27:57,260 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:27:57,261 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:27:57,262 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:27:57,262 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:27:57,262 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:27:57,262 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:27:57,688 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 16:27:58,139 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 16:27:58,143 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 16:27:58,330 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 16:27:58,336 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 16:27:58,341 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 16:27:58,344 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 16:27:58,346 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 16:27:58,350 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 16:27:58,362 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 16:27:58,366 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 16:27:58,383 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 16:27:58,435 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:27:58,435 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:27:58,454 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:27:59,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 16:27:59,606 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 16:27:59,608 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:28:00,397 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:28:00,398 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:28:00,399 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:28:00,400 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:28:00,400 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:28:00,400 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:28:01,697 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:28:01,697 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:28:01,707 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:28:01,725 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:28:01,741 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:28:05,405 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "GET / HTTP/1.1" 200 -
2025-06-08 16:28:05,541 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,542 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,555 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,570 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,589 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,657 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,671 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,687 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,699 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,726 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,738 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,739 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:05,771 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:05] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:28:06,044 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:06] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:28:06,063 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:06] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:28:06,541 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:28:06,552 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:28:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:29:46,257 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 16:29:46,752 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:29:47,908 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:29:47,908 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:29:47,909 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:29:47,909 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:29:47,910 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:29:47,910 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:29:49,312 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:29:49,313 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:29:49,322 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:29:49,343 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:29:49,362 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:30:06,038 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\test_model_call.py', reloading
2025-06-08 16:30:06,280 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:30:07,149 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:30:07,149 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:30:07,150 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:30:07,150 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:30:07,150 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:30:07,151 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:30:08,340 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:30:08,341 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:30:08,349 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:30:08,372 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:30:08,398 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:30:56,103 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 16:30:56,629 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:30:57,859 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:30:57,859 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:30:57,860 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:30:57,860 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:30:57,860 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:30:57,861 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:30:59,262 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:30:59,263 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:30:59,277 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:30:59,300 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:30:59,323 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:31:16,911 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:16] "GET / HTTP/1.1" 200 -
2025-06-08 16:31:16,935 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:16] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:31:16,936 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:31:16,941 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:16] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:31:16,970 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:16] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,080 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,086 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,111 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,122 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,128 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,136 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,141 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,146 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,165 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:31:17,184 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:31:17,198 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:31:17,678 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:31:17,686 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:31:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:32:35,904 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 16:32:36,186 - werkzeug - INFO -  * Restarting with stat
2025-06-08 16:32:37,200 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 16:32:37,207 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 16:32:37,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 16:32:37,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 16:32:37,211 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 16:32:37,211 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 16:32:38,944 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 16:32:38,946 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 16:32:38,961 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 16:32:39,012 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 16:32:39,035 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 16:36:18,226 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:36:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:41:18,257 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:41:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:46:18,206 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:46:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:49:55,573 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:49:55] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 16:51:18,218 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:51:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:52:46,389 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:52:46] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 16:52:47,772 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:52:47] "[32mPOST /api/idphoto/create HTTP/1.1[0m" 302 -
2025-06-08 16:52:47,790 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:52:47] "[31m[1mGET /api/auth/login?next=/api/idphoto/create HTTP/1.1[0m" 405 -
2025-06-08 16:54:19,319 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:54:19] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 16:54:20,135 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:54:20] "[32mPOST /api/idphoto/create HTTP/1.1[0m" 302 -
2025-06-08 16:54:20,149 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:54:20] "[31m[1mGET /api/auth/login?next=/api/idphoto/create HTTP/1.1[0m" 405 -
2025-06-08 16:55:48,912 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:55:48] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 16:55:49,823 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:55:49] "[33mPOST /auth/login HTTP/1.1[0m" 404 -
2025-06-08 16:55:49,857 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:55:49] "[32mPOST /api/idphoto/create HTTP/1.1[0m" 302 -
2025-06-08 16:55:49,874 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:55:49] "[31m[1mGET /api/auth/login?next=/api/idphoto/create HTTP/1.1[0m" 405 -
2025-06-08 16:56:18,217 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:18] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:56:28,399 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "GET / HTTP/1.1" 200 -
2025-06-08 16:56:28,450 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,452 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,456 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,473 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,474 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,499 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,521 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,532 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,535 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,549 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,597 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,605 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,614 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:56:28,651 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:56:28,671 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:56:29,149 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:56:29,155 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:56:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:57:32,403 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:32] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-08 16:57:41,665 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:41] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-08 16:57:47,034 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "GET / HTTP/1.1" 200 -
2025-06-08 16:57:47,061 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,073 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,078 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,084 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,090 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,117 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,174 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,174 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,175 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,179 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,196 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,197 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,197 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 16:57:47,221 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:57:47,242 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 16:57:47,727 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:57:47,740 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 16:57:47,798 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:57:47] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-08 16:58:19,364 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:58:19] "GET / HTTP/1.1" 200 -
2025-06-08 16:58:19,368 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:58:19] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-08 16:58:19,380 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:58:19] "[32mPOST /api/idphoto/create HTTP/1.1[0m" 302 -
2025-06-08 16:58:19,394 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 16:58:19] "[31m[1mGET /api/auth/login?next=/api/idphoto/create HTTP/1.1[0m" 405 -
2025-06-08 17:01:28,421 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:01:28,422 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:01:28,423 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:01:28,423 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:01:28,423 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:01:28,423 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:01:28,879 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 17:01:29,503 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 17:01:29,507 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 17:01:29,736 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 17:01:29,741 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 17:01:29,748 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 17:01:29,752 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 17:01:29,755 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 17:01:29,759 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 17:01:29,764 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 17:01:29,768 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 17:01:29,770 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 17:01:29,797 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:01:29,798 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:01:29,819 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:01:29,839 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:01:29,839 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:01:29,839 - backend.utils.scheduler - WARNING - 调度器已在运行
2025-06-08 17:01:29,840 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:01:29,840 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:01:29,841 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 17:01:29,847 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 17:01:29,849 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 17:01:29,851 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 17:01:29,855 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 17:01:29,865 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 17:01:29,868 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 17:01:29,870 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 17:01:29,873 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 17:01:29,882 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 17:01:29,884 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 17:01:29,886 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 17:02:04,438 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:02:04,439 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:02:04,439 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:02:04,440 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:02:04,440 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:02:04,440 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:02:04,914 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 17:02:05,446 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 17:02:05,450 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 17:02:05,667 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 17:02:05,675 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 17:02:05,680 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 17:02:05,684 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 17:02:05,688 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 17:02:05,697 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 17:02:05,701 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 17:02:05,704 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 17:02:05,706 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 17:02:05,739 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:02:05,740 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:02:05,756 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:02:05,768 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:02:05,768 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:02:05,769 - backend.utils.scheduler - WARNING - 调度器已在运行
2025-06-08 17:02:05,769 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:02:05,769 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:02:05,771 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 17:02:05,772 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 17:02:05,778 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 17:02:05,781 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 17:02:05,786 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 17:02:05,795 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 17:02:05,797 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 17:02:05,799 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 17:02:05,801 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 17:02:05,804 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 17:02:05,805 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 17:02:05,808 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 17:02:20,271 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:02:20] "[31m[1mPOST /api/auth/register HTTP/1.1[0m" 400 -
2025-06-08 17:02:20,476 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:02:20] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-08 17:02:20,481 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:02:20] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:02:20,495 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:02:20,516 - superspider.permissions - INFO - 📊 用户使用记录: download_count=0, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:02:20,520 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=5
2025-06-08 17:02:20,533 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:02:21,981 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第1次
2025-06-08 17:02:21,981 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:02:21,989 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:02:21] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:02:47,739 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:02:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:07:31,304 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "GET / HTTP/1.1" 200 -
2025-06-08 17:07:31,322 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,324 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,325 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,345 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,355 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,356 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,359 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,382 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,438 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,438 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,445 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,460 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,461 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:07:31,482 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:07:31,493 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:07:31,985 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:07:31,993 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:07:32,028 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:07:32] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-08 17:08:55,526 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "GET / HTTP/1.1" 200 -
2025-06-08 17:08:55,551 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,645 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,670 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,698 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,712 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,728 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,737 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,748 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,760 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,781 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,783 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,794 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,795 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:08:55,811 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:08:55,846 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:55] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:08:56,310 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:08:56,315 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:08:56,352 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:08:56] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-08 17:11:16,048 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 17:11:17,073 - werkzeug - INFO -  * Restarting with stat
2025-06-08 17:11:18,308 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:11:18,308 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:11:18,309 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:11:18,309 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:11:18,309 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:11:18,309 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:11:19,621 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:11:19,621 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:11:19,630 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:11:19,656 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 17:11:19,669 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 17:11:46,764 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 17:11:46,961 - werkzeug - INFO -  * Restarting with stat
2025-06-08 17:11:47,762 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:11:47,762 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:11:47,763 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:11:47,763 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:11:47,763 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:11:47,763 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:11:48,932 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:11:48,933 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:11:48,942 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:11:48,959 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 17:11:48,975 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 17:12:21,456 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:12:21] "GET /api/idphoto/status HTTP/1.1" 200 -
2025-06-08 17:12:21,707 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:12:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-08 17:12:21,716 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:12:21,720 - superspider.permissions - INFO - 📊 用户使用记录: download_count=1, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:12:21,723 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=4
2025-06-08 17:12:21,727 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:12:21,727 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:12:21,727 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:12:23,238 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:12:23,243 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:12:23,460 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第2次
2025-06-08 17:12:23,460 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:12:23,460 - superspider.idphoto - INFO - 单张照片长度: 145202
2025-06-08 17:12:23,461 - superspider.idphoto - INFO - 排版照片长度: 356450
2025-06-08 17:12:23,461 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:12:23,464 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:12:23] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:13:16,253 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "GET / HTTP/1.1" 200 -
2025-06-08 17:13:16,405 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,420 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,550 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,585 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,620 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,631 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,656 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,706 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,721 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,736 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,738 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:16,771 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:16] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:13:17,121 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:17] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:13:17,137 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:17] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:13:17,615 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:13:17,623 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:13:17,667 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:17] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-08 17:13:53,667 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:53] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:13:53,672 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:13:53,696 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:53] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:13:54,017 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:13:54] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:14:05,949 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:05] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 17:14:08,044 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:08] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-08 17:14:15,773 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-08 17:14:25,830 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "GET / HTTP/1.1" 200 -
2025-06-08 17:14:25,866 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,881 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,899 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,901 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,904 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,933 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,955 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:25,963 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:25] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:26,020 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:14:26,031 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:26,039 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:26,047 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:26,051 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:26,113 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:14:26,202 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:14:26,301 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:14:26,705 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:14:26,721 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:14:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:16:28,289 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "GET / HTTP/1.1" 200 -
2025-06-08 17:16:28,326 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,379 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,397 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,427 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,427 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,429 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,446 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,455 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:16:28,494 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,512 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,546 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,578 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,580 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,594 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:28,624 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:16:28,637 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:16:29,205 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:16:29,218 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:16:43,069 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:16:43,069 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:16:43,070 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:16:43,070 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:16:43,070 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:16:43,071 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:16:43,581 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 17:16:44,239 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 17:16:44,244 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 17:16:44,455 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 17:16:44,462 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 17:16:44,467 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 17:16:44,470 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 17:16:44,476 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 17:16:44,480 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 17:16:44,484 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 17:16:44,486 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 17:16:44,488 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 17:16:44,520 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:16:44,520 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:16:44,538 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:16:46,674 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 17:16:46,675 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 17:16:46,679 - werkzeug - INFO -  * Restarting with stat
2025-06-08 17:16:47,456 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:16:47,457 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:16:47,458 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:16:47,458 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:16:47,459 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:16:47,459 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:16:48,590 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:16:48,591 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:16:48,600 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:16:48,615 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 17:16:48,636 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 17:16:55,799 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:55] "GET / HTTP/1.1" 200 -
2025-06-08 17:16:55,822 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:16:55,861 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:55] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:16:55,899 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:55] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:16:55,947 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:55] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,047 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,108 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,117 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,128 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:16:56,132 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,226 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,246 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,289 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,292 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,294 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:16:56,535 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:16:56,743 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:16:56,951 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:16:56,974 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:16:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:17:14,508 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:17:14,517 - superspider.permissions - INFO - 📊 用户使用记录: download_count=2, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:17:14,518 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=3
2025-06-08 17:17:14,520 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:17:14,520 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:17:14,520 - superspider.idphoto - INFO - 请求参数: {'height': 531, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:17:16,356 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:17:16,359 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:17:16,672 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第3次
2025-06-08 17:17:16,672 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:17:16,673 - superspider.idphoto - INFO - 单张照片长度: 207866
2025-06-08 17:17:16,673 - superspider.idphoto - INFO - 排版照片长度: 485190
2025-06-08 17:17:16,673 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:17:16,680 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:17:16] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:18:03,042 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:18:03,043 - superspider.permissions - INFO - 📊 用户使用记录: download_count=3, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:18:03,043 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=2
2025-06-08 17:18:03,045 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:18:03,045 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:18:03,045 - superspider.idphoto - INFO - 请求参数: {'height': 531, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:18:04,113 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:18:04,115 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:18:04,470 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第4次
2025-06-08 17:18:04,470 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:18:04,471 - superspider.idphoto - INFO - 单张照片长度: 208010
2025-06-08 17:18:04,471 - superspider.idphoto - INFO - 排版照片长度: 485950
2025-06-08 17:18:04,471 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:18:04,475 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:18:04] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:18:25,572 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:18:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:18:25,577 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:18:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:18:25,580 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:18:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:18:25,646 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:18:25] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:20:21,320 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 17:20:21,596 - werkzeug - INFO -  * Restarting with stat
2025-06-08 17:20:22,484 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:20:22,485 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:20:22,485 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:20:22,485 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:20:22,486 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:20:22,486 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:20:24,003 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:20:24,004 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:20:24,012 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:20:24,041 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 17:20:24,073 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 17:20:46,706 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "GET / HTTP/1.1" 200 -
2025-06-08 17:20:46,732 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,800 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,827 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,828 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,846 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,914 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,919 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:20:46,935 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:46,969 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:46] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:47,000 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:47,001 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:47,012 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:47,031 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:47,037 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:20:47,069 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:20:47,225 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:20:47,602 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:20:47,630 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:20:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:21:00,187 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:21:00,192 - superspider.permissions - INFO - 📊 用户使用记录: download_count=4, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:21:00,192 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=1
2025-06-08 17:21:00,194 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:21:00,194 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:21:00,195 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:21:01,289 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:21:01,291 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:21:01,353 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 17:21:01,502 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第5次
2025-06-08 17:21:01,502 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:21:01,502 - superspider.idphoto - INFO - 单张照片长度: 129938
2025-06-08 17:21:01,503 - superspider.idphoto - INFO - 排版照片长度: 320650
2025-06-08 17:21:01,503 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:21:01,506 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:21:01] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:23:19,878 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:23:19,879 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:23:19,880 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:23:19,880 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:23:19,881 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:23:19,881 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:23:20,344 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 17:23:20,918 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 17:23:20,922 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 17:23:21,109 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 17:23:21,116 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 17:23:21,122 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 17:23:21,124 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 17:23:21,131 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 17:23:21,136 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 17:23:21,139 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 17:23:21,142 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 17:23:21,144 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 17:23:21,180 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:23:21,181 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:23:21,195 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:23:21,251 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 17:23:21,252 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 17:23:21,254 - werkzeug - INFO -  * Restarting with stat
2025-06-08 17:23:22,055 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 17:23:22,055 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 17:23:22,056 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 17:23:22,056 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 17:23:22,057 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 17:23:22,057 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 17:23:23,252 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 17:23:23,253 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 17:23:23,264 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 17:23:23,282 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 17:23:23,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 17:23:26,403 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "GET / HTTP/1.1" 200 -
2025-06-08 17:23:26,436 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,439 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,504 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,512 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,589 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-08 17:23:26,591 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,621 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,638 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,648 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,658 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,687 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,706 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,780 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,787 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:23:26,889 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:23:26,905 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:23:27,419 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:23:27,538 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:23:37,521 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:23:37,533 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:23:37,533 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 17:23:37,534 - superspider.permissions - WARNING - 🚫 用户 test 下载次数已达上限: 5/5
2025-06-08 17:23:37,544 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:23:37] "[31m[1mPOST /api/idphoto/create HTTP/1.1[0m" 429 -
2025-06-08 17:24:13,274 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:24:13,277 - superspider.permissions - INFO - 📊 用户使用记录: download_count=0, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:24:13,277 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=5
2025-06-08 17:24:13,278 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:24:13,279 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:24:13,279 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:24:14,289 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:24:14,291 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:24:14,343 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 17:24:14,567 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第1次
2025-06-08 17:24:14,568 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:24:14,568 - superspider.idphoto - INFO - 单张照片长度: 129938
2025-06-08 17:24:14,568 - superspider.idphoto - INFO - 排版照片长度: 320650
2025-06-08 17:24:14,568 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:24:14,572 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:24:14] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:24:48,532 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:24:48,537 - superspider.permissions - INFO - 📊 用户使用记录: download_count=1, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:24:48,538 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=4
2025-06-08 17:24:48,540 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:24:48,540 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:24:48,543 - superspider.idphoto - INFO - 请求参数: {'height': 626, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:24:49,846 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:24:49,848 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:24:49,960 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ0A
2025-06-08 17:24:50,139 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第2次
2025-06-08 17:24:50,139 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:24:50,139 - superspider.idphoto - INFO - 单张照片长度: 230862
2025-06-08 17:24:50,139 - superspider.idphoto - INFO - 排版照片长度: 519246
2025-06-08 17:24:50,139 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:24:50,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:24:50] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:25:02,189 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "GET / HTTP/1.1" 200 -
2025-06-08 17:25:02,246 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,250 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,264 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,270 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,272 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,283 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,334 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,342 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,367 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,369 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,390 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,400 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,407 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:25:02,436 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:25:02,460 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:25:02,935 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:25:02,945 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:25:13,621 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:25:13,628 - superspider.permissions - INFO - 📊 用户使用记录: download_count=2, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:25:13,629 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=3
2025-06-08 17:25:13,631 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:25:13,632 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:25:13,635 - superspider.idphoto - INFO - 请求参数: {'height': 531, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:25:14,943 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:25:14,945 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:25:15,059 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ0A
2025-06-08 17:25:15,256 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第3次
2025-06-08 17:25:15,256 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:25:15,256 - superspider.idphoto - INFO - 单张照片长度: 207866
2025-06-08 17:25:15,257 - superspider.idphoto - INFO - 排版照片长度: 485190
2025-06-08 17:25:15,257 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:25:15,262 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:15] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:25:33,140 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:25:33,142 - superspider.permissions - INFO - 📊 用户使用记录: download_count=3, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:25:33,143 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=2
2025-06-08 17:25:33,144 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:25:33,144 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:25:33,145 - superspider.idphoto - INFO - 请求参数: {'height': 531, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:25:34,125 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:25:34,128 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:25:34,239 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ0A
2025-06-08 17:25:34,409 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第4次
2025-06-08 17:25:34,409 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:25:34,410 - superspider.idphoto - INFO - 单张照片长度: 208010
2025-06-08 17:25:34,410 - superspider.idphoto - INFO - 排版照片长度: 485950
2025-06-08 17:25:34,410 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:25:34,417 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:25:34] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:26:10,291 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:26:10,292 - superspider.permissions - INFO - 📊 用户使用记录: download_count=4, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:26:10,292 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=1
2025-06-08 17:26:10,295 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:26:10,295 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:26:10,296 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:26:11,380 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:26:11,384 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:26:11,475 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 17:26:11,634 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第5次
2025-06-08 17:26:11,636 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:26:11,637 - superspider.idphoto - INFO - 单张照片长度: 130438
2025-06-08 17:26:11,637 - superspider.idphoto - INFO - 排版照片长度: 321886
2025-06-08 17:26:11,637 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:26:11,640 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:26:11] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:27:31,794 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:27:31,796 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:27:31,797 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 17:27:31,797 - superspider.permissions - WARNING - 🚫 用户 test 下载次数已达上限: 5/5
2025-06-08 17:27:31,798 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:27:31] "[31m[1mPOST /api/idphoto/create HTTP/1.1[0m" 429 -
2025-06-08 17:29:05,446 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "GET / HTTP/1.1" 200 -
2025-06-08 17:29:05,470 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,470 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,473 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,484 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,485 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,503 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,503 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,505 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,517 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,547 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,551 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,557 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,583 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 17:29:05,597 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:29:05,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:05] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 17:29:06,097 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:29:06,104 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:29:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:33:59,247 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:33:59,254 - superspider.permissions - INFO - 📊 用户使用记录: download_count=-1, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:33:59,255 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=6
2025-06-08 17:33:59,269 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:33:59,269 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:33:59,269 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:34:01,185 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:34:01,192 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:34:01,257 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 17:34:01,408 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第0次
2025-06-08 17:34:01,408 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:34:01,408 - superspider.idphoto - INFO - 单张照片长度: 144058
2025-06-08 17:34:01,409 - superspider.idphoto - INFO - 排版照片长度: 354138
2025-06-08 17:34:01,409 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:34:01,418 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:34:01] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:34:06,120 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:34:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:34:38,855 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 17:34:38,857 - superspider.permissions - INFO - 📊 用户使用记录: download_count=0, api_call_count=0, api_calls_this_minute=0
2025-06-08 17:34:38,858 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=5
2025-06-08 17:34:38,864 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 17:34:38,864 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 17:34:38,865 - superspider.idphoto - INFO - 请求参数: {'height': 626, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 17:34:40,432 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 17:34:40,437 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 17:34:40,555 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ0A
2025-06-08 17:34:40,721 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第1次
2025-06-08 17:34:40,721 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 17:34:40,722 - superspider.idphoto - INFO - 单张照片长度: 281258
2025-06-08 17:34:40,722 - superspider.idphoto - INFO - 排版照片长度: 642014
2025-06-08 17:34:40,722 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 17:34:40,730 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:34:40] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 17:39:06,216 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:39:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:44:06,202 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:44:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:49:06,201 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:49:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:54:06,210 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:54:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 17:59:06,201 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 17:59:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:04:29,215 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:04:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:09:29,197 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:09:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:14:29,201 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:14:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:19:29,200 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:19:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:24:29,191 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:24:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:29:29,197 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:29:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:34:29,191 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:34:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:47:05,176 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:47:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:48:12,845 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\idphoto_api.py', reloading
2025-06-08 18:48:14,287 - werkzeug - INFO -  * Restarting with stat
2025-06-08 18:48:16,008 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 18:48:16,009 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 18:48:16,010 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 18:48:16,010 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 18:48:16,010 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 18:48:16,010 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 18:48:17,576 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 18:48:17,579 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 18:48:17,592 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 18:48:17,615 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 18:48:17,643 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 18:49:29,181 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:49:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:54:29,178 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:54:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:55:07,933 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:07] "GET / HTTP/1.1" 200 -
2025-06-08 18:55:07,968 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 18:55:07,983 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:07] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 18:55:07,985 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:07] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:07,986 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:07] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 18:55:07,988 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:07] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,010 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,023 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,035 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,041 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,044 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,070 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,055 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,084 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 18:55:08,150 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 18:55:08,163 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 18:55:08,648 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:55:08,655 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:55:16,611 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 18:55:16,646 - superspider.permissions - INFO - 📊 用户使用记录: download_count=1, api_call_count=0, api_calls_this_minute=0
2025-06-08 18:55:16,650 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=4
2025-06-08 18:55:16,683 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 18:55:16,684 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 18:55:16,685 - superspider.idphoto - INFO - 请求参数: {'height': 626, 'width': 413, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 18:55:18,548 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 18:55:18,556 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 18:55:18,683 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ0A
2025-06-08 18:55:18,754 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第2次
2025-06-08 18:55:18,755 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 18:55:18,755 - superspider.idphoto - INFO - 单张照片长度: 281258
2025-06-08 18:55:18,755 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 18:55:18,759 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:55:18] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 18:57:15,191 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "GET / HTTP/1.1" 200 -
2025-06-08 18:57:15,216 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,218 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,222 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,233 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,234 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,259 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,271 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,281 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,284 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,353 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,355 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,360 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,360 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 18:57:15,400 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 18:57:15,411 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 18:57:15,901 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:57:15,907 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 18:57:26,131 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 18:57:26,134 - superspider.permissions - INFO - 📊 用户使用记录: download_count=2, api_call_count=0, api_calls_this_minute=0
2025-06-08 18:57:26,134 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=3
2025-06-08 18:57:26,138 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 18:57:26,139 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 18:57:26,140 - superspider.idphoto - INFO - 请求参数: {'height': 378, 'width': 260, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 18:57:27,477 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 18:57:27,484 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 18:57:27,530 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQQA
2025-06-08 18:57:27,540 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第3次
2025-06-08 18:57:27,543 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 18:57:27,544 - superspider.idphoto - INFO - 单张照片长度: 116742
2025-06-08 18:57:27,544 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 18:57:27,546 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 18:57:27] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 19:02:15,919 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:02:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:07:16,171 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:07:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:08:52,729 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "GET / HTTP/1.1" 200 -
2025-06-08 19:08:52,755 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,776 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,784 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,813 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,817 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,821 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,828 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,837 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,842 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,873 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,885 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,895 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,897 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:08:52,927 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:08:52,940 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:52] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:08:53,424 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:53] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:08:53,431 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:08:53] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:09:03,951 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:09:03,956 - superspider.permissions - INFO - 📊 用户使用记录: download_count=3, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:09:03,956 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=2
2025-06-08 19:09:03,966 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 19:09:03,966 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 19:09:03,967 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 19:09:05,710 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 19:09:05,717 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 19:09:05,787 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 19:09:05,811 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第4次
2025-06-08 19:09:05,812 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 19:09:05,814 - superspider.idphoto - INFO - 单张照片长度: 148010
2025-06-08 19:09:05,815 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 19:09:05,817 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:09:05] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 19:13:54,168 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:13:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:14:00,021 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "GET / HTTP/1.1" 200 -
2025-06-08 19:14:00,069 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,073 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,080 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,089 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,098 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,104 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,156 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,158 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,165 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,167 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,188 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,189 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,202 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:14:00,254 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:14:00,275 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:14:00,749 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:14:00,757 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:14:10,766 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:14:10,770 - superspider.permissions - INFO - 📊 用户使用记录: download_count=4, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:14:10,770 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=1
2025-06-08 19:14:10,777 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 19:14:10,777 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 19:14:10,777 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 19:14:12,320 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 19:14:12,330 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 19:14:12,395 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 19:14:12,411 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第5次
2025-06-08 19:14:12,411 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 19:14:12,411 - superspider.idphoto - INFO - 单张照片长度: 148010
2025-06-08 19:14:12,411 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 19:14:12,414 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:12] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 19:14:27,922 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:14:27,924 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:14:27,925 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 19:14:27,927 - superspider.permissions - WARNING - 🚫 用户 test 下载次数已达上限: 5/5
2025-06-08 19:14:27,928 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:27] "[31m[1mPOST /api/idphoto/create HTTP/1.1[0m" 429 -
2025-06-08 19:14:31,263 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:14:31,267 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:14:31,267 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 19:14:31,268 - superspider.permissions - WARNING - 🚫 用户 test 下载次数已达上限: 5/5
2025-06-08 19:14:31,269 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:31] "[31m[1mPOST /api/idphoto/create HTTP/1.1[0m" 429 -
2025-06-08 19:14:36,141 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:14:36,146 - superspider.permissions - INFO - 📊 用户使用记录: download_count=5, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:14:36,149 - superspider.permissions - INFO - 📥 下载检查结果: can_download=False, remaining=0
2025-06-08 19:14:36,151 - superspider.permissions - WARNING - 🚫 用户 test 下载次数已达上限: 5/5
2025-06-08 19:14:36,154 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:14:36] "[31m[1mPOST /api/idphoto/create HTTP/1.1[0m" 429 -
2025-06-08 19:19:01,951 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:19:01] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:20:59,697 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "GET / HTTP/1.1" 200 -
2025-06-08 19:20:59,748 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,758 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,791 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,817 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,843 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,843 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,915 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,929 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,923 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,943 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,956 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,960 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,961 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:20:59,986 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:20:59] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:21:00,018 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:00] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:21:00,499 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:21:00,512 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:00] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:21:28,229 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "GET / HTTP/1.1" 200 -
2025-06-08 19:21:28,292 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,297 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,299 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,304 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,317 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,337 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,372 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,416 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,465 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,471 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,586 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,670 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:28,822 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:21:28,852 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:21:29,268 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:21:29,285 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:21:57,807 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "GET / HTTP/1.1" 200 -
2025-06-08 19:21:57,839 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,877 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,879 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,883 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,895 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,903 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,923 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,975 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,984 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:57,989 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:57] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:58,004 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:58,005 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:58,020 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:21:58,060 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:21:58,083 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:21:58,550 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:21:58,560 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:21:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:23:01,420 - superspider.permissions - INFO - 🔍 开始检查用户 test 的搜索限制
2025-06-08 19:23:01,421 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:23:01,435 - superspider.permissions - INFO - 📊 用户使用记录: download_count=0, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:23:01,436 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=5
2025-06-08 19:23:01,437 - superspider.permissions - INFO - 📥 每日下载检查: can_download=True, remaining=5, error=None
2025-06-08 19:23:01,437 - superspider.permissions - INFO - 🔍 检查用户 test 的 api 限制
2025-06-08 19:23:01,440 - superspider.permissions - INFO - 📊 用户使用记录: download_count=0, api_call_count=0, api_calls_this_minute=0
2025-06-08 19:23:01,441 - superspider.permissions - INFO - 🔄 API检查结果: can_api=True, remaining=3
2025-06-08 19:23:01,441 - superspider.permissions - INFO - 🔄 API频率检查: can_api=True, remaining=3, error=None
2025-06-08 19:23:01,442 - superspider.permissions - INFO - ✅ 用户 test 通过所有限制检查，允许执行操作
2025-06-08 19:23:01,452 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-06-08 19:23:01,948 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-06-08 19:23:06,820 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-08 19:23:06,868 - superspider.permissions - INFO - 记录API调用 - 用户: test, 今日第1次
2025-06-08 19:23:06,920 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第1次
2025-06-08 19:23:06,924 - superspider.permissions - INFO - 用户 test 执行搜索操作成功
2025-06-08 19:23:06,936 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:23:06] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-06-08 19:23:07,588 - backend.api.search_api - INFO - 用户 test 创建搜索记录: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-06-08 19:23:07,602 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:23:07] "POST /api/search/record HTTP/1.1" 200 -
2025-06-08 19:25:58,067 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "GET / HTTP/1.1" 200 -
2025-06-08 19:25:58,107 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,114 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,117 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,147 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,152 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,185 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,213 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,247 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,286 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,288 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-08 19:25:58,299 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,305 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,332 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:25:58,394 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:25:58,415 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:25:58,871 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:25:58,884 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:25:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:26:45,515 - superspider.permissions - INFO - 🔍 检查用户 test 的 download 限制
2025-06-08 19:26:45,537 - superspider.permissions - INFO - 📊 用户使用记录: download_count=1, api_call_count=1, api_calls_this_minute=1
2025-06-08 19:26:45,539 - superspider.permissions - INFO - 📥 下载检查结果: can_download=True, remaining=4
2025-06-08 19:26:45,560 - superspider.idphoto - INFO - 开始为用户 test 制作证件照
2025-06-08 19:26:45,562 - superspider.idphoto - INFO - 调用HivisionIDPhotos API: http://127.0.0.1:8080/idphoto
2025-06-08 19:26:45,563 - superspider.idphoto - INFO - 请求参数: {'height': 413, 'width': 295, 'human_matting_model': 'modnet_photographic_portrait_matting', 'face_detect_model': 'mtcnn', 'hd': True, 'dpi': 300, 'face_align': True}
2025-06-08 19:26:47,987 - superspider.idphoto - INFO - HivisionIDPhotos响应状态: 200
2025-06-08 19:26:47,993 - superspider.idphoto - INFO - HivisionIDPhotos响应结果: status=True
2025-06-08 19:26:48,074 - superspider.idphoto - INFO - 最终图片base64前50字符: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScA
2025-06-08 19:26:48,097 - superspider.permissions - INFO - 记录下载操作 - 用户: test, 今日第2次
2025-06-08 19:26:48,097 - superspider.idphoto - INFO - 用户 test 证件照制作成功
2025-06-08 19:26:48,100 - superspider.idphoto - INFO - 单张照片长度: 146814
2025-06-08 19:26:48,102 - superspider.idphoto - INFO - 准备返回响应: success=True
2025-06-08 19:26:48,104 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:26:48] "POST /api/idphoto/create HTTP/1.1" 200 -
2025-06-08 19:30:17,308 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 19:30:17,308 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 19:30:17,309 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 19:30:17,309 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 19:30:17,309 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 19:30:17,310 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 19:30:17,897 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 19:30:19,358 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 19:30:19,384 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 19:30:19,805 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 19:30:19,823 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 19:30:19,839 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 19:30:19,851 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 19:30:19,856 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 19:30:19,859 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 19:30:19,872 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 19:30:19,875 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 19:30:19,882 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 19:30:19,924 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 19:30:19,925 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 19:30:19,942 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 19:30:22,154 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 19:30:22,155 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 19:30:22,157 - werkzeug - INFO -  * Restarting with stat
2025-06-08 19:30:23,464 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 19:30:23,464 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 19:30:23,466 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 19:30:23,466 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 19:30:23,466 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 19:30:23,466 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 19:30:25,335 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 19:30:25,335 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 19:30:25,350 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 19:30:25,373 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 19:30:25,415 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 19:30:25,554 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "GET / HTTP/1.1" 200 -
2025-06-08 19:30:25,571 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,584 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "GET / HTTP/1.1" 200 -
2025-06-08 19:30:25,585 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,620 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,622 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,638 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,665 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,682 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,717 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,734 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-08 19:30:25,789 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,835 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,853 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:25,888 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:25] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:30:26,062 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:30:26,164 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:30:26,557 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:30:26,568 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:30:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:31:02,164 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 19:31:02,164 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 19:31:02,165 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 19:31:02,165 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 19:31:02,166 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 19:31:02,166 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 19:31:02,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 19:31:03,678 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 19:31:03,682 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 19:31:04,064 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 19:31:04,069 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 19:31:04,083 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 19:31:04,086 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 19:31:04,094 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 19:31:04,099 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 19:31:04,112 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 19:31:04,129 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 19:31:04,136 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 19:31:04,186 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 19:31:04,189 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 19:31:04,208 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 19:31:09,450 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 19:31:09,456 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 19:31:09,461 - werkzeug - INFO -  * Restarting with stat
2025-06-08 19:31:10,750 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 19:31:10,754 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 19:31:10,756 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 19:31:10,756 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 19:31:10,756 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 19:31:10,756 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 19:31:12,561 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 19:31:12,562 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 19:31:12,576 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 19:31:12,612 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 19:31:12,643 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 19:31:12,760 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "GET / HTTP/1.1" 200 -
2025-06-08 19:31:12,779 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,782 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,785 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,813 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,825 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,861 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,894 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,896 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,897 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-08 19:31:12,906 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,925 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,930 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:12,939 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:12] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,364 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "GET / HTTP/1.1" 200 -
2025-06-08 19:31:13,385 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,411 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,418 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,425 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,438 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,439 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,461 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,491 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,493 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,493 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,509 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,560 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,564 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:31:13,594 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:31:13,647 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:13] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:31:14,110 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:14] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:31:14,128 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:31:14] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:36:16,555 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:36:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:37:23,449 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 19:37:23,450 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 19:37:23,454 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 19:37:23,454 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 19:37:23,454 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 19:37:23,454 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 19:37:23,930 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 19:37:24,775 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 19:37:24,780 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 19:37:25,122 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 19:37:25,137 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 19:37:25,154 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 19:37:25,161 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 19:37:25,164 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 19:37:25,174 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 19:37:25,178 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 19:37:25,180 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 19:37:25,186 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 19:37:25,221 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 19:37:25,222 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 19:37:25,238 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 19:37:25,276 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 19:37:25,277 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 19:37:25,279 - werkzeug - INFO -  * Restarting with stat
2025-06-08 19:37:26,192 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 19:37:26,192 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 19:37:26,193 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 19:37:26,193 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 19:37:26,193 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 19:37:26,194 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 19:37:27,368 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 19:37:27,369 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 19:37:27,385 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 19:37:27,396 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 19:37:27,425 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 19:37:27,510 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "GET / HTTP/1.1" 200 -
2025-06-08 19:37:27,602 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,604 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,642 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,647 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,691 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,710 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,744 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,747 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,756 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,756 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,763 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 19:37:27,770 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-08 19:37:27,911 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:37:27,924 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:27] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 19:37:28,423 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:28] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:37:28,429 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:28] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:37:52,679 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:52] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 19:37:53,558 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:37:53] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:42:28,460 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:42:28] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:47:28,444 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:47:28] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:52:28,447 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:52:28] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 19:57:13,771 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:57:13] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 19:57:29,162 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 19:57:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:02:29,163 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:02:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:06:39,254 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 20:06:39,255 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 20:06:39,256 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 20:06:39,256 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 20:06:39,256 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 20:06:39,256 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 20:06:39,885 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-08 20:06:40,826 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-08 20:06:40,834 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-08 20:06:41,137 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-08 20:06:41,142 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-08 20:06:41,150 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-08 20:06:41,154 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-08 20:06:41,160 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-08 20:06:41,167 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-08 20:06:41,174 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-08 20:06:41,177 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-08 20:06:41,182 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-08 20:06:41,216 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 20:06:41,217 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 20:06:41,235 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 20:06:41,275 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-08 20:06:41,276 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-08 20:06:41,278 - werkzeug - INFO -  * Restarting with stat
2025-06-08 20:06:42,180 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-08 20:06:42,181 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-08 20:06:42,182 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-08 20:06:42,182 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-08 20:06:42,182 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-08 20:06:42,182 - superspider - INFO - 定时任务调度器初始化成功
2025-06-08 20:06:43,394 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-08 20:06:43,397 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-08 20:06:43,408 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-08 20:06:43,428 - werkzeug - WARNING -  * Debugger is active!
2025-06-08 20:06:43,467 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-08 20:06:43,547 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "GET / HTTP/1.1" 200 -
2025-06-08 20:06:43,577 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,583 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,602 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,609 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,613 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,617 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,617 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,637 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,653 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,719 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,726 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,733 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,744 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:06:43,772 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:06:43,799 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:43] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:06:44,277 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:06:44,284 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:06:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:08:40,722 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "GET / HTTP/1.1" 200 -
2025-06-08 20:08:40,748 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,750 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,755 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,768 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,769 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,782 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,797 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,834 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,834 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,836 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,846 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,852 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,867 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:08:40,881 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:08:40,892 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:08:41,390 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:08:41,396 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:08:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:13:41,411 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:13:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:18:41,406 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:18:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:21:53,436 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "GET / HTTP/1.1" 200 -
2025-06-08 20:21:53,460 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,471 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,471 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,472 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,478 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,499 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,521 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,525 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,544 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,557 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,558 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,609 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,611 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:21:53,630 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:21:53,643 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:53] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:21:54,133 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:21:54,140 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:21:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:26:54,155 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:26:54] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:28:24,859 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "GET / HTTP/1.1" 200 -
2025-06-08 20:28:24,877 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,878 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,881 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,888 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,906 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,908 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,924 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:24,940 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:24] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:25,044 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:25,044 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:25,048 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:25,063 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:25,078 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:28:25,108 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:28:25,121 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:28:25,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:28:25,616 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:28:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:30:30,703 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "GET / HTTP/1.1" 200 -
2025-06-08 20:30:30,716 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,719 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,720 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,736 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,749 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,752 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,768 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,799 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,801 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,819 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,827 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,831 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,835 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:30:30,859 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:30:30,871 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:30:31,361 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:30:31,366 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:30:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:32:50,558 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "GET / HTTP/1.1" 200 -
2025-06-08 20:32:50,575 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,581 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,596 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,599 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,619 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,626 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,632 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,632 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,660 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,679 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,682 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,684 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:32:50,709 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:32:50,719 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:32:51,208 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:32:51,214 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:32:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:37:51,235 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:37:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:38:01,462 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:38:01] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 20:38:02,272 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:38:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:39:06,874 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:06] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 20:39:09,208 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "GET / HTTP/1.1" 200 -
2025-06-08 20:39:09,233 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,239 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,253 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,287 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,308 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,327 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,330 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,434 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,472 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,490 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,496 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,519 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,524 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:39:09,545 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:39:09,560 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:39:10,038 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:39:10,058 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:39:10,618 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:10] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-08 20:39:11,428 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:39:11] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:44:10,089 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:44:18,796 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "GET / HTTP/1.1" 200 -
2025-06-08 20:44:18,838 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,840 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,848 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,862 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,869 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,869 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,880 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,885 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,891 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,893 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,897 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,919 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,928 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-08 20:44:18,953 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:44:18,971 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-08 20:44:19,454 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:44:19,460 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:44:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:49:19,485 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:49:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:54:19,462 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:54:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 20:59:19,482 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 20:59:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:04:19,474 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:04:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:09:19,502 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:09:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:14:19,458 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:14:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:19:19,463 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:19:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:24:19,466 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:24:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:29:19,481 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:29:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:34:19,455 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:34:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:39:19,465 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:39:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:44:19,456 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:44:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:49:19,463 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:49:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:54:19,456 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:54:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 21:59:19,455 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 21:59:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:04:19,455 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:04:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:09:19,530 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:09:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:14:29,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:14:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:19:20,133 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:19:20] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:24:29,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:24:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:29:29,145 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:29:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:34:29,131 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:34:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:39:29,141 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:39:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:44:29,135 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:44:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:49:29,128 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:49:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:54:29,126 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:54:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 22:59:29,140 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 22:59:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:04:29,131 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:04:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:09:29,136 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:09:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:42:36,342 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:42:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:46:51,157 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:46:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:49:19,449 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:49:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:54:19,445 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:54:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-08 23:59:19,452 - werkzeug - INFO - 127.0.0.1 - - [08/Jun/2025 23:59:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:04:19,451 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:04:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:09:19,455 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:09:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:14:19,512 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:14:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:18:29,832 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:18:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:18:29,838 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:18:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:23:29,849 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:23:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:28:29,841 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:28:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:33:29,851 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:33:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:38:29,852 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:38:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:43:29,842 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:43:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:48:29,849 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:48:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:53:29,849 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:53:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 00:58:29,841 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 00:58:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:03:29,847 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:03:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:08:22,191 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:08:22] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-09 01:08:29,831 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:08:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:08:32,981 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:08:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:13:29,850 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:13:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:18:29,851 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:18:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:24:29,107 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:24:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:29:29,128 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:29:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:34:29,105 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:34:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:34:44,200 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:34:44] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-09 01:34:49,509 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:34:49] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:36:52,376 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:36:52,932 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:36:54,018 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:36:54,019 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:36:54,019 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:36:54,019 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:36:54,020 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:36:54,020 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:36:55,290 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:36:55,341 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:36:55,342 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:36:55,373 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:36:55,389 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 5 个，VIP账号 0 个
2025-06-09 01:36:55,389 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:37:07,820 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:37:08,013 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:37:08,794 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:37:08,795 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:37:08,796 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:37:08,796 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:37:08,796 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:37:08,796 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:37:09,802 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:37:09,845 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:37:09,845 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:37:09,860 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:37:09,871 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:37:09,887 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:37:19,253 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:37:19,439 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:37:20,159 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:37:20,160 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:37:20,161 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:37:20,161 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:37:20,162 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:37:20,162 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:37:21,144 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:37:21,184 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:37:21,187 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:37:21,205 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:37:21,221 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:37:21,237 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:37:36,721 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:37:37,129 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:37:37,817 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:37:37,818 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:37:37,819 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:37:37,819 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:37:37,819 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:37:37,819 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:37:38,928 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:37:38,978 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:37:38,979 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:37:38,992 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:37:39,007 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:37:39,021 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:39:29,131 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:39:29] "[35m[1mGET /api/permission/check HTTP/1.1[0m" 500 -
2025-06-09 01:44:04,471 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:44:04,472 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:44:04,472 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:44:04,473 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:44:04,473 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:44:04,473 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:44:04,939 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-09 01:44:05,629 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-09 01:44:05,634 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-09 01:44:05,826 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-09 01:44:05,843 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:44:05,846 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-09 01:44:05,849 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-09 01:44:05,857 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-09 01:44:05,862 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-09 01:44:05,870 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-09 01:44:05,874 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-09 01:44:05,878 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-09 01:44:05,919 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:44:05,919 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:44:05,941 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:44:05,976 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-09 01:44:05,978 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 01:44:05,981 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:44:06,705 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:44:06,705 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:44:06,706 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:44:06,706 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:44:06,707 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:44:06,707 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:44:07,767 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:44:07,813 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:44:07,813 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:44:07,831 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:44:07,843 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:44:07,860 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:44:10,728 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:10] "[35m[1mGET /api/permission/check HTTP/1.1[0m" 500 -
2025-06-09 01:44:29,804 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "GET / HTTP/1.1" 200 -
2025-06-09 01:44:29,879 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,881 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "GET /static/css/user.css HTTP/1.1" 200 -
2025-06-09 01:44:29,887 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,912 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,913 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,937 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,942 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-06-09 01:44:29,986 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,986 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,990 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:29,999 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:29] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:30,009 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:30,044 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:44:30,161 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[33mGET /api/auth/check-auth HTTP/1.1[0m" 404 -
2025-06-09 01:44:30,165 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:44:30,171 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[33mGET /api/auth/check-auth HTTP/1.1[0m" 404 -
2025-06-09 01:44:30,684 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[35m[1mGET /api/permission/check HTTP/1.1[0m" 500 -
2025-06-09 01:44:30,719 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:30] "[35m[1mGET /api/permission/check HTTP/1.1[0m" 500 -
2025-06-09 01:44:32,891 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:32] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:44:34,583 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:44:34] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:44:51,710 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-06-09 01:44:52,176 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:44:54,167 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:44:54,171 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:44:54,173 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:44:54,173 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:44:54,174 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:44:54,174 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:44:56,272 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:44:56,338 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:44:56,338 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:44:56,364 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:44:56,387 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:44:56,405 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:45:05,817 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-06-09 01:45:06,345 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:45:07,321 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:45:07,321 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:45:07,323 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:45:07,323 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:45:07,324 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:45:07,324 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:45:08,540 - superspider - ERROR - 注册认证API路由失败: replace expected at least 2 arguments, got 1
2025-06-09 01:45:08,587 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:45:08,588 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:45:08,610 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:45:08,618 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:45:08,635 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:45:17,734 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "GET / HTTP/1.1" 200 -
2025-06-09 01:45:17,764 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,775 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,788 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,809 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,813 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,818 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,839 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,842 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,859 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,860 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,874 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,875 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,892 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:17,910 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[33mGET /api/auth/check-auth HTTP/1.1[0m" 404 -
2025-06-09 01:45:17,918 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:45:17,931 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:17] "[33mGET /api/auth/check-auth HTTP/1.1[0m" 404 -
2025-06-09 01:45:18,419 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:18] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:45:18,424 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:18] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:45:27,032 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "GET / HTTP/1.1" 200 -
2025-06-09 01:45:27,053 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,062 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,073 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,075 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,076 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,078 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,089 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,100 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,107 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,118 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,124 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,138 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,150 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:45:27,164 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[33mGET /api/auth/check-auth HTTP/1.1[0m" 404 -
2025-06-09 01:45:27,173 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:45:27,174 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[33mGET /api/auth/check-auth HTTP/1.1[0m" 404 -
2025-06-09 01:45:27,662 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:45:27,668 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:27] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:45:31,346 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:45:31] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:47:04,199 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:47:04] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:47:32,604 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:47:32] "[33mGET /api/auth/captcha HTTP/1.1[0m" 404 -
2025-06-09 01:47:52,514 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:47:52] "GET /api/status HTTP/1.1" 200 -
2025-06-09 01:49:01,537 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\captcha.py', reloading
2025-06-09 01:49:01,820 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:49:02,623 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:49:02,623 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:49:02,624 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:49:02,624 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:49:02,624 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:49:02,624 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:49:03,913 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:49:03,913 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:49:03,934 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:49:03,953 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:49:03,968 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:49:21,135 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "GET / HTTP/1.1" 200 -
2025-06-09 01:49:21,159 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,167 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,170 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,186 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,191 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,194 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,201 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,221 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,249 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,267 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,288 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,292 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,306 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:49:21,426 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-09 01:49:21,459 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-09 01:49:21,482 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:49:21,831 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:49:21,835 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:21] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:49:43,602 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:43] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-06-09 01:49:49,505 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 01:49:50,527 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:50] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:49:54,698 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:49:54] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:50:17,258 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:17] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:50:33,595 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:33] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 400 -
2025-06-09 01:50:33,605 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:33] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:50:39,884 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 01:50:40,904 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:40] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:50:43,019 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:43] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:50:54,111 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET / HTTP/1.1" 200 -
2025-06-09 01:50:54,241 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-09 01:50:54,242 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/css/user.css HTTP/1.1" 200 -
2025-06-09 01:50:54,479 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-06-09 01:50:54,480 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-06-09 01:50:54,480 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-06-09 01:50:54,480 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-06-09 01:50:54,574 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-06-09 01:50:54,574 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-06-09 01:50:54,839 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-06-09 01:50:54,844 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-06-09 01:50:54,854 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-09 01:50:54,856 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-06-09 01:50:54,894 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:54] "GET /static/js/limit-handler.js HTTP/1.1" 200 -
2025-06-09 01:50:55,261 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-09 01:50:55,274 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:50:55,519 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-09 01:50:55,571 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-09 01:50:55,579 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:50:55,589 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:50:55,769 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-06-09 01:50:55,876 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:50:55] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:51:07,088 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:07] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 400 -
2025-06-09 01:51:07,354 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:07] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:51:14,467 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:14] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 400 -
2025-06-09 01:51:14,734 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:14] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:51:20,604 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:20] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 400 -
2025-06-09 01:51:20,921 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:20] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:51:29,753 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 01:51:30,771 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:30] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:51:35,316 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:35] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:51:46,011 - superspider.auth - INFO - 验证码发送到 13157568559: 492747
2025-06-09 01:51:46,012 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:46] "POST /api/auth/send-sms HTTP/1.1" 200 -
2025-06-09 01:51:57,362 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:51:57] "POST /api/auth/sms-login HTTP/1.1" 200 -
2025-06-09 01:54:16,378 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:54:16,776 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:54:17,914 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:54:17,915 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:54:17,917 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:54:17,918 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:54:17,918 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:54:17,918 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:54:19,353 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:54:19,355 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:54:19,384 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:54:19,404 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:54:19,424 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:54:30,929 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:54:31,101 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:54:31,853 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:54:31,854 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:54:31,855 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:54:31,855 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:54:31,855 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:54:31,856 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:54:33,081 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:54:33,083 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:54:33,101 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:54:33,118 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:54:33,131 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:54:55,901 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:54:56,102 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:54:56,814 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:54:56,815 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:54:56,816 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:54:56,816 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:54:56,816 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:54:56,816 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:54:57,914 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:54:57,915 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:54:57,938 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:54:57,955 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:54:57,977 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:55:32,070 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\auth_api.py', reloading
2025-06-09 01:55:32,268 - werkzeug - INFO -  * Restarting with stat
2025-06-09 01:55:33,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 01:55:33,210 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 01:55:33,213 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 01:55:33,213 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 01:55:33,214 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 01:55:33,214 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 01:55:34,427 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 01:55:34,429 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 01:55:34,448 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 01:55:34,465 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 01:55:34,480 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 01:55:43,067 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "GET / HTTP/1.1" 200 -
2025-06-09 01:55:43,213 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,215 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,519 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,519 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,520 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,520 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,532 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-06-09 01:55:43,542 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,846 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,846 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,847 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,847 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:43,852 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:43] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:55:44,192 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:55:44,247 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:55:44,435 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:55:44,514 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:55:44,564 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:55:44,683 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:55:44,761 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:55:44,904 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:44] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-09 01:55:46,948 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:46] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:55:56,399 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:56] "[31m[1mGET /api/permission/check HTTP/1.1[0m" 401 -
2025-06-09 01:55:58,329 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 01:55:59,655 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:55:59] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:56:03,219 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:03] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:56:15,214 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 01:56:16,230 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:16] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:56:19,849 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:19] "GET / HTTP/1.1" 200 -
2025-06-09 01:56:19,865 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,110 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,187 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,188 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,189 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,189 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,189 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,416 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,512 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,512 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,512 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,513 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,524 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:56:20,851 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:56:20,851 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:20] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:56:21,097 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:21] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:56:21,176 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:21] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:56:21,197 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:21] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:56:21,344 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:21] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:56:21,482 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:21] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:56:21,525 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:56:21] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-09 01:57:21,328 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "GET / HTTP/1.1" 200 -
2025-06-09 01:57:21,340 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,550 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,650 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,650 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,650 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,650 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,668 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,864 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,978 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,979 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,979 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,980 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:21,989 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:21] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:22,306 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:57:22,307 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:57:22,582 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:57:22,616 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:57:22,616 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:57:22,821 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:57:22,898 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:57:22,950 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:22] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-09 01:57:38,503 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "GET / HTTP/1.1" 200 -
2025-06-09 01:57:38,519 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 01:57:38,764 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 01:57:38,830 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 01:57:38,831 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:38,832 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 01:57:38,837 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:38,855 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:38] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,076 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,153 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,171 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,172 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,172 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,176 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 01:57:39,496 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:57:39,498 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:57:39,759 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:57:39,808 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 01:57:39,808 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:57:39,990 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 01:57:40,071 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 01:57:40,126 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:40] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-09 01:57:59,916 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:57:59] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-09 01:58:00,182 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:58:00] "GET /api/auth/profile HTTP/1.1" 200 -
2025-06-09 01:58:42,387 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:58:42] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 01:58:56,368 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:58:56] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 01:58:57,382 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 01:58:57] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:00:13,732 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:00:13] "[31m[1mGET /api/search/history HTTP/1.1[0m" 401 -
2025-06-09 02:00:56,397 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:00:56] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:02:39,526 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "GET / HTTP/1.1" 200 -
2025-06-09 02:02:39,681 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 02:02:39,726 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 02:02:39,993 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:39,993 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 02:02:39,993 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:39,994 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:39] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 02:02:40,039 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-06-09 02:02:40,057 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-06-09 02:02:40,306 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:02:40,310 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:40,311 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:40,314 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:40,351 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:40,396 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 02:02:40,720 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:02:40,722 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:02:40,975 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:02:41,042 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:41] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:02:41,042 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:02:41,225 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:41] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:02:41,290 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:02:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:03:08,682 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 02:03:08,682 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 02:03:08,683 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 02:03:08,683 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 02:03:08,683 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 02:03:08,683 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 02:03:09,191 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-09 02:03:09,887 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-09 02:03:09,891 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-09 02:03:10,173 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-09 02:03:10,209 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-09 02:03:10,213 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-09 02:03:10,216 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-09 02:03:10,224 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-09 02:03:10,228 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-09 02:03:10,234 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-09 02:03:10,242 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-09 02:03:10,246 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-09 02:03:10,287 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 02:03:10,289 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 02:03:10,315 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 02:03:10,347 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-09 02:03:10,348 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 02:03:10,350 - werkzeug - INFO -  * Restarting with stat
2025-06-09 02:03:11,189 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 02:03:11,191 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 02:03:11,195 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 02:03:11,195 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 02:03:11,196 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 02:03:11,196 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 02:03:12,548 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 02:03:12,549 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 02:03:12,567 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 02:03:12,582 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 02:03:12,603 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 02:03:13,326 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "GET / HTTP/1.1" 200 -
2025-06-09 02:03:13,347 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,554 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,666 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,667 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,668 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,670 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-06-09 02:03:13,671 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,866 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-06-09 02:03:13,976 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,996 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,996 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,997 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:13,997 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:13] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 02:03:14,339 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:03:14,370 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:03:14,587 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:03:14,648 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:03:14,680 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:03:14,837 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:03:14,897 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:14] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:03:31,211 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:31] "GET /api/auth/logout HTTP/1.1" 200 -
2025-06-09 02:03:42,499 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:42] "POST /api/auth/login HTTP/1.1" 200 -
2025-06-09 02:03:43,508 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:03:43] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:08:15,419 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:08:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:13:15,398 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:13:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:18:09,230 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:18:09] "[31m[1mGET /api/search/history HTTP/1.1[0m" 401 -
2025-06-09 02:18:15,404 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:18:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:22:24,983 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 02:22:24,984 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 02:22:24,985 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 02:22:24,985 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 02:22:24,986 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 02:22:24,986 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 02:22:25,823 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-09 02:22:26,617 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-09 02:22:26,622 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-09 02:22:26,901 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-09 02:22:26,936 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-09 02:22:26,943 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-09 02:22:26,952 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-09 02:22:26,958 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-09 02:22:26,960 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-09 02:22:26,969 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-09 02:22:26,973 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-09 02:22:26,976 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-09 02:22:27,013 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 02:22:27,019 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 02:22:27,052 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 02:22:27,088 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-09 02:22:27,088 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 02:22:27,090 - werkzeug - INFO -  * Restarting with stat
2025-06-09 02:22:28,055 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 02:22:28,056 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 02:22:28,057 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 02:22:28,057 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 02:22:28,058 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 02:22:28,058 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 02:22:29,415 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 02:22:29,416 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 02:22:29,434 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 02:22:29,453 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 02:22:29,469 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 02:22:29,553 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "GET / HTTP/1.1" 200 -
2025-06-09 02:22:29,587 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,587 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,907 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,908 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,908 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,930 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,936 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:29,937 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:29] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-06-09 02:22:30,307 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:30,307 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:30,307 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:30,308 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:30,310 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:30,672 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:22:30,724 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:22:30,919 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:22:30,985 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:30] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:22:31,042 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:22:31,168 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:22:31,234 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:22:31,390 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:31] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-09 02:22:35,117 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:35,118 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:35] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:35,118 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:35] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:35,137 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:35] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-09 02:22:50,229 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "GET / HTTP/1.1" 200 -
2025-06-09 02:22:50,248 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,490 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,560 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,562 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,563 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,579 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,614 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,803 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,879 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,885 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,888 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,898 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:50,928 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:50] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 02:22:51,126 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:22:51,280 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:22:51,282 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:22:51,443 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:22:51,597 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:22:51,630 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:22:51,775 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:22:51,958 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-06-09 02:22:52,096 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:22:52] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-09 02:27:33,875 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 02:27:33,875 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 02:27:33,876 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 02:27:33,876 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 02:27:33,877 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 02:27:33,877 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 02:27:34,725 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-06-09 02:27:35,718 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-06-09 02:27:35,727 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-06-09 02:27:35,994 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-06-09 02:27:36,038 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-06-09 02:27:36,042 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-06-09 02:27:36,049 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-06-09 02:27:36,053 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-06-09 02:27:36,057 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-06-09 02:27:36,065 - superspider - INFO - 已注册VIP账号管理API路由到 /api/vip-accounts
2025-06-09 02:27:36,068 - superspider - INFO - 已注册使用统计API路由到 /api/usage
2025-06-09 02:27:36,071 - superspider - INFO - 已注册证件照API路由到 /api/idphoto
2025-06-09 02:27:36,125 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 02:27:36,125 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 02:27:36,159 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 02:27:36,234 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-09 02:27:36,234 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 02:27:36,237 - werkzeug - INFO -  * Restarting with stat
2025-06-09 02:27:37,468 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-09 02:27:37,468 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-09 02:27:37,469 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-09 02:27:37,470 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-09 02:27:37,470 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-09 02:27:37,470 - superspider - INFO - 定时任务调度器初始化成功
2025-06-09 02:27:38,805 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-09 02:27:38,806 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-09 02:27:38,829 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-09 02:27:38,840 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 02:27:38,856 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-06-09 02:27:38,952 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:38] "GET / HTTP/1.1" 200 -
2025-06-09 02:27:39,340 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,342 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,363 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,372 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,417 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,471 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,675 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,706 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-06-09 02:27:39,782 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,793 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,794 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,856 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:39,985 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:39] "[36mGET /static/js/limit-handler.js HTTP/1.1[0m" 304 -
2025-06-09 02:27:40,024 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:27:40,333 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:27:40,341 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-09 02:27:40,353 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-06-09 02:27:40,416 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:27:40,747 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "GET /api/auth/captcha HTTP/1.1" 200 -
2025-06-09 02:27:40,815 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:27:41,002 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-06-09 02:27:41,202 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:41] "GET /api/search/history?page=1&per_page=10 HTTP/1.1" 200 -
2025-06-09 02:27:41,379 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 02:27:41] "GET /api/activation/stats HTTP/1.1" 200 -

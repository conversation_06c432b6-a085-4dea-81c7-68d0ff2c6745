#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础爬虫类
定义所有爬虫应实现的通用接口
"""

import abc
import logging
import os
import time
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)

class BaseSpider(abc.ABC):
    """基础爬虫抽象类，定义所有爬虫的通用接口"""
    
    def __init__(self, name: str):
        """
        初始化爬虫
        
        Args:
            name: 爬虫名称
        """
        self.name = name
        self.start_time = time.time()
        
        logger.info(f"初始化爬虫: {self.name}")
    
    @abc.abstractmethod
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行爬虫任务
        
        Args:
            params: 任务参数
            
        Returns:
            执行结果
        """
        pass
    
    def get_execution_time(self) -> float:
        """
        获取爬虫执行时间（秒）
        
        Returns:
            执行时间
        """
        return time.time() - self.start_time
    
    def check_status(self) -> Dict[str, Any]:
        """
        检查爬虫状态
        
        Returns:
            状态信息
        """
        return {
            "name": self.name,
            "status": "ready",
            "execution_time": self.get_execution_time()
        } 
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
激活码管理API模块
提供激活码生成、使用和管理相关的API
"""

import logging
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

from backend.main import db
from backend.models.user import User
from backend.models.activation_code import ActivationCode
from backend.utils.permissions import require_role, PermissionManager

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
activation_api = Blueprint('activation_api', __name__, url_prefix='/activation')

@activation_api.route('/generate', methods=['POST'])
@require_role('super_admin')
def generate_codes():
    """
    生成激活码（仅超级管理员）
    
    请求体:
        {
            "days": 30,
            "count": 10,
            "description": "30天Pro用户激活码"
        }
    
    响应:
        {
            "success": true,
            "message": "激活码生成成功",
            "data": {
                "generated_count": 10,
                "codes": [
                    {
                        "id": 1,
                        "code": "ABCD-EFGH-IJKL-MNOP",
                        "days": 30,
                        "description": "30天Pro用户激活码"
                    }
                ]
            }
        }
    """
    try:
        data = request.get_json()
        
        days = data.get('days')
        count = data.get('count', 1)
        description = data.get('description', '')
        
        if not days or days <= 0:
            return jsonify({
                "success": False,
                "message": "请提供有效的天数",
                "data": None
            }), 400
        
        if count <= 0 or count > 100:
            return jsonify({
                "success": False,
                "message": "生成数量必须在1-100之间",
                "data": None
            }), 400
        
        # 验证天数是否为预设值
        allowed_days = [1, 7, 30, 90, 365]
        if days not in allowed_days:
            return jsonify({
                "success": False,
                "message": f"天数必须为以下值之一: {', '.join(map(str, allowed_days))}",
                "data": None
            }), 400
        
        generated_codes = []
        
        for i in range(count):
            # 生成激活码
            activation_code = ActivationCode(
                days=days,
                created_by=current_user.id,
                description=description
            )
            
            db.session.add(activation_code)
            db.session.flush()  # 获取ID但不提交
            
            generated_codes.append(activation_code.to_dict(include_sensitive=True))
        
        db.session.commit()
        
        logger.info(f"管理员 {current_user.username} 生成了 {count} 个 {days} 天的激活码")
        
        return jsonify({
            "success": True,
            "message": f"成功生成 {count} 个激活码",
            "data": {
                "generated_count": count,
                "codes": generated_codes
            }
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"生成激活码失败: {e}")
        return jsonify({
            "success": False,
            "message": "生成激活码失败",
            "data": None
        }), 500

@activation_api.route('/use', methods=['POST'])
@login_required
def use_activation_code():
    """
    使用激活码升级账户
    
    请求体:
        {
            "code": "ABCD-EFGH-IJKL-MNOP"
        }
    
    响应:
        {
            "success": true,
            "message": "激活码使用成功，账户已升级为Pro用户",
            "data": {
                "old_role": "normal_user",
                "new_role": "pro_user",
                "days_added": 30,
                "vip_expire_date": "2024-01-01T00:00:00"
            }
        }
    """
    try:
        data = request.get_json()
        
        code = data.get('code', '').strip().upper()
        
        if not code:
            return jsonify({
                "success": False,
                "message": "请输入激活码",
                "data": None
            }), 400
        
        # 查找激活码
        activation_code = ActivationCode.find_by_code(code)
        
        if not activation_code:
            return jsonify({
                "success": False,
                "message": "激活码不存在",
                "data": None
            }), 404
        
        if activation_code.is_used:
            return jsonify({
                "success": False,
                "message": "激活码已被使用",
                "data": None
            }), 400
        
        # 记录用户原始角色
        old_role = current_user.role
        
        # 使用激活码
        success, message = activation_code.use_code(current_user.id)
        
        if not success:
            return jsonify({
                "success": False,
                "message": message,
                "data": None
            }), 400
        
        # 升级用户为Pro用户
        PermissionManager.upgrade_user_to_pro(current_user, activation_code.days)
        
        logger.info(f"用户 {current_user.username} 使用激活码 {code} 升级为Pro用户 ({activation_code.days}天)")
        
        return jsonify({
            "success": True,
            "message": f"激活码使用成功，账户已升级为Pro用户 ({activation_code.get_days_display()})",
            "data": {
                "old_role": old_role,
                "new_role": current_user.role,
                "days_added": activation_code.days,
                "vip_expire_date": current_user.vip_expire_date.isoformat() if current_user.vip_expire_date else None
            }
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"使用激活码失败: {e}")
        return jsonify({
            "success": False,
            "message": "使用激活码失败",
            "data": None
        }), 500

@activation_api.route('/list', methods=['GET'])
@require_role('super_admin')
def list_activation_codes():
    """
    获取激活码列表（仅超级管理员）
    
    查询参数:
        - page: 页码 (默认1)
        - per_page: 每页数量 (默认20)
        - status: 状态筛选 (all/used/unused)
        - days: 天数筛选
    
    响应:
        {
            "success": true,
            "message": "获取激活码列表成功",
            "data": {
                "codes": [...],
                "pagination": {
                    "page": 1,
                    "per_page": 20,
                    "total": 100,
                    "pages": 5
                },
                "stats": {
                    "total": 100,
                    "used": 30,
                    "unused": 70,
                    "usage_rate": 30.0
                }
            }
        }
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        status = request.args.get('status', 'all')
        days = request.args.get('days', type=int)
        
        # 构建查询
        query = ActivationCode.query
        
        # 状态筛选
        if status == 'used':
            query = query.filter_by(is_used=True)
        elif status == 'unused':
            query = query.filter_by(is_used=False)
        
        # 天数筛选
        if days:
            query = query.filter_by(days=days)
        
        # 按创建时间倒序
        query = query.order_by(ActivationCode.created_at.desc())
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        codes = [code.to_dict(include_sensitive=True) for code in pagination.items]
        
        # 获取统计信息
        stats = ActivationCode.get_usage_stats()
        
        return jsonify({
            "success": True,
            "message": "获取激活码列表成功",
            "data": {
                "codes": codes,
                "pagination": {
                    "page": pagination.page,
                    "per_page": pagination.per_page,
                    "total": pagination.total,
                    "pages": pagination.pages
                },
                "stats": stats
            }
        })
        
    except Exception as e:
        logger.error(f"获取激活码列表失败: {e}")
        return jsonify({
            "success": False,
            "message": "获取激活码列表失败",
            "data": None
        }), 500

@activation_api.route('/stats', methods=['GET'])
@require_role('super_admin')
def get_activation_stats():
    """
    获取激活码统计信息（仅超级管理员）
    
    响应:
        {
            "success": true,
            "message": "获取统计信息成功",
            "data": {
                "total": 100,
                "used": 30,
                "unused": 70,
                "usage_rate": 30.0,
                "by_days": {
                    "1": {"total": 10, "used": 5},
                    "7": {"total": 20, "used": 8},
                    "30": {"total": 50, "used": 15},
                    "90": {"total": 20, "used": 2}
                }
            }
        }
    """
    try:
        # 基础统计
        stats = ActivationCode.get_usage_stats()
        
        # 按天数统计
        days_stats = {}
        for days in [1, 7, 30, 90, 365]:
            total = ActivationCode.query.filter_by(days=days).count()
            used = ActivationCode.query.filter_by(days=days, is_used=True).count()
            
            if total > 0:
                days_stats[str(days)] = {
                    "total": total,
                    "used": used,
                    "unused": total - used,
                    "usage_rate": round((used / total * 100), 2)
                }
        
        stats["by_days"] = days_stats
        
        return jsonify({
            "success": True,
            "message": "获取统计信息成功",
            "data": stats
        })
        
    except Exception as e:
        logger.error(f"获取激活码统计失败: {e}")
        return jsonify({
            "success": False,
            "message": "获取统计信息失败",
            "data": None
        }), 500

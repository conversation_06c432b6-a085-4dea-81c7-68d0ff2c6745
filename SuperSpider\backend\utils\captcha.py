#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证码生成工具
生成图片验证码用于登录验证
"""

import random
import string
import io
import base64
from PIL import Image, ImageDraw, ImageFont
import os

class CaptchaGenerator:
    """验证码生成器"""
    
    def __init__(self, width=120, height=40, font_size=24):
        """
        初始化验证码生成器
        
        Args:
            width: 图片宽度
            height: 图片高度
            font_size: 字体大小
        """
        self.width = width
        self.height = height
        self.font_size = font_size
        
        # 验证码字符集（去除容易混淆的字符）
        self.chars = string.ascii_uppercase + string.digits
        self.chars = self.chars.replace('0', '').replace('O', '').replace('I', '').replace('1', '')
        
        # 颜色配置
        self.bg_color = (255, 255, 255)  # 白色背景
        self.text_colors = [
            (0, 0, 0),      # 黑色
            (128, 0, 0),    # 深红
            (0, 128, 0),    # 深绿
            (0, 0, 128),    # 深蓝
            (128, 128, 0),  # 深黄
        ]
        self.line_colors = [
            (128, 128, 128),  # 灰色
            (192, 192, 192),  # 浅灰
        ]
    
    def generate_code(self, length=4):
        """
        生成验证码字符串
        
        Args:
            length: 验证码长度
            
        Returns:
            验证码字符串
        """
        return ''.join(random.choices(self.chars, k=length))
    
    def create_image(self, code):
        """
        创建验证码图片
        
        Args:
            code: 验证码字符串
            
        Returns:
            PIL Image对象
        """
        # 创建图片
        image = Image.new('RGB', (self.width, self.height), self.bg_color)
        draw = ImageDraw.Draw(image)
        
        # 尝试加载字体
        try:
            # Windows系统字体路径
            font_paths = [
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/calibri.ttf',
                '/System/Library/Fonts/Arial.ttf',  # macOS
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            ]
            
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, self.font_size)
                    break
            
            if font is None:
                font = ImageFont.load_default()
                
        except Exception:
            font = ImageFont.load_default()
        
        # 绘制干扰线
        self._draw_interference_lines(draw)
        
        # 绘制验证码字符
        self._draw_text(draw, code, font)
        
        # 添加噪点
        self._add_noise(draw)
        
        return image
    
    def _draw_interference_lines(self, draw):
        """绘制干扰线"""
        for _ in range(random.randint(3, 6)):
            x1 = random.randint(0, self.width)
            y1 = random.randint(0, self.height)
            x2 = random.randint(0, self.width)
            y2 = random.randint(0, self.height)
            color = random.choice(self.line_colors)
            draw.line([(x1, y1), (x2, y2)], fill=color, width=1)
    
    def _draw_text(self, draw, code, font):
        """绘制验证码文字"""
        char_width = self.width // len(code)
        
        for i, char in enumerate(code):
            # 随机位置和颜色
            x = char_width * i + random.randint(5, 15)
            y = random.randint(5, 15)
            color = random.choice(self.text_colors)
            
            # 绘制字符
            draw.text((x, y), char, fill=color, font=font)
    
    def _add_noise(self, draw):
        """添加噪点"""
        for _ in range(random.randint(50, 100)):
            x = random.randint(0, self.width - 1)
            y = random.randint(0, self.height - 1)
            color = random.choice(self.text_colors + self.line_colors)
            draw.point((x, y), fill=color)
    
    def generate_captcha(self, length=4):
        """
        生成完整的验证码（代码+图片）
        
        Args:
            length: 验证码长度
            
        Returns:
            tuple: (验证码字符串, base64编码的图片)
        """
        # 生成验证码
        code = self.generate_code(length)
        
        # 创建图片
        image = self.create_image(code)
        
        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return code, f"data:image/png;base64,{image_base64}"

# 全局验证码生成器实例
captcha_generator = CaptchaGenerator()

def generate_login_captcha():
    """
    生成登录验证码
    
    Returns:
        tuple: (验证码字符串, base64图片)
    """
    return captcha_generator.generate_captcha()

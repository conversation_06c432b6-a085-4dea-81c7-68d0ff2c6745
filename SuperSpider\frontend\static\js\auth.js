/**
 * SuperSpider 用户认证相关脚本
 * 处理用户登录、注册、个人资料等功能
 */

// 初始化用户认证相关功能
function initAuthFeatures() {
    console.log("初始化用户认证功能...");

    // 登录按钮点击事件
    const loginBtn = document.getElementById('login-btn');
    console.log("登录按钮元素:", loginBtn);

    if (loginBtn) {
        console.log("为登录按钮添加点击事件");
        loginBtn.addEventListener('click', () => {
            console.log("登录按钮被点击");
            openModal('login-modal');
        });
    }

    // 注册按钮点击事件
    const registerBtn = document.getElementById('register-btn');
    console.log("注册按钮元素:", registerBtn);

    if (registerBtn) {
        console.log("为注册按钮添加点击事件");
        registerBtn.addEventListener('click', () => {
            console.log("注册按钮被点击");
            openModal('register-modal');
        });
    }

    // 关闭模态框按钮
    document.querySelectorAll('.close-modal, .modal-overlay').forEach(el => {
        el.addEventListener('click', (e) => {
            // 移除条件判断，无论点击按钮还是按钮内的图标都会关闭模态框
            closeAllModals();
        });
    });

    // 切换到注册模态框
    const switchToRegister = document.getElementById('switch-to-register');
    if (switchToRegister) {
        switchToRegister.addEventListener('click', (e) => {
            e.preventDefault();
            closeModal('login-modal');
            openModal('register-modal');
        });
    }

    // 切换到登录模态框
    const switchToLogin = document.getElementById('switch-to-login');
    if (switchToLogin) {
        switchToLogin.addEventListener('click', (e) => {
            e.preventDefault();
            closeModal('register-modal');
            openModal('login-modal');
        });
    }

    // 登录方式切换
    const loginTabs = document.querySelectorAll('.login-tabs .tab-btn');
    loginTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            const targetTab = e.target.dataset.tab;
            switchLoginTab(targetTab);
        });
    });

    // 密码登录表单提交
    const passwordLoginForm = document.getElementById('password-login-form');
    if (passwordLoginForm) {
        passwordLoginForm.addEventListener('submit', handlePasswordLogin);
    }

    // 验证码登录表单提交
    const smsLoginForm = document.getElementById('sms-login-form');
    if (smsLoginForm) {
        smsLoginForm.addEventListener('submit', handleSmsLogin);
    }

    // 发送验证码按钮
    const sendSmsBtn = document.getElementById('send-sms-btn');
    if (sendSmsBtn) {
        sendSmsBtn.addEventListener('click', handleSendSms);
    }

    // 初始化验证码
    refreshCaptcha();

    // 注册表单提交
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }

    // 密码输入框（简化版，无强度检测）
    const passwordInput = document.getElementById('register-password');
    if (passwordInput) {
        // 只做基本的确认密码匹配检查
        passwordInput.addEventListener('input', validateConfirmPassword);
    }

    // 确认密码验证
    const confirmPasswordInput = document.getElementById('register-confirm-password');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validateConfirmPassword);
    }

    // 用户菜单下拉切换
    const userDropdownToggle = document.getElementById('user-dropdown-toggle');
    if (userDropdownToggle) {
        userDropdownToggle.addEventListener('click', toggleUserDropdown);
    }

    // 点击页面其他地方关闭下拉菜单
    document.addEventListener('click', (e) => {
        const dropdown = document.querySelector('.dropdown-menu');
        const toggle = document.getElementById('user-dropdown-toggle');
        const userMenu = document.querySelector('.user-menu');

        // 如果点击的不是下拉菜单、切换按钮或用户菜单，则关闭下拉菜单
        if (dropdown && toggle && userMenu &&
            !dropdown.contains(e.target) &&
            !toggle.contains(e.target) &&
            !userMenu.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });

    // 退出登录链接
    const logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
        logoutLink.addEventListener('click', handleLogout);
    }

    // 个人资料链接
    const profileLink = document.getElementById('profile-link');
    if (profileLink) {
        profileLink.addEventListener('click', (e) => {
            e.preventDefault();
            openModal('profile-modal');
            loadUserProfile();
        });
    }

    // 下载记录链接
    const downloadsLink = document.getElementById('downloads-link');
    if (downloadsLink) {
        downloadsLink.addEventListener('click', (e) => {
            e.preventDefault();
            // 跳转到下载记录页面或打开下载记录模态框
            openModal('downloads-modal');
            loadDownloadHistory();
        });
    }

    // 设置链接
    const settingsLink = document.getElementById('settings-link');
    if (settingsLink) {
        settingsLink.addEventListener('click', (e) => {
            e.preventDefault();
            // 跳转到设置页面或打开设置模态框
            openModal('settings-modal');
        });
    }

    // 忘记密码链接
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            closeModal('login-modal');
            openModal('forgot-password-modal');
        });
    }

    // 返回登录按钮
    const backToLoginBtn = document.getElementById('back-to-login');
    if (backToLoginBtn) {
        backToLoginBtn.addEventListener('click', (e) => {
            e.preventDefault();
            closeModal('forgot-password-modal');
            openModal('login-modal');
        });
    }

    // 显示微信二维码按钮
    const showWechatQrBtn = document.getElementById('show-wechat-qr');
    if (showWechatQrBtn) {
        showWechatQrBtn.addEventListener('click', (e) => {
            e.preventDefault();
            closeModal('forgot-password-modal');
            openModal('wechat-modal');
        });
    }

    // 检查用户登录状态
    checkAuthStatus();

    // 强制初始化工具区域状态
    setTimeout(() => {
        forceUpdateToolsVisibility();
    }, 100);
}

// 打开模态框
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.classList.add('modal-open');
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }
}

// 关闭所有模态框
function closeAllModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
    });
    document.body.classList.remove('modal-open');
}

// 切换登录方式
function switchLoginTab(tabName) {
    // 切换标签按钮状态
    document.querySelectorAll('.login-tabs .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 切换表单显示
    document.querySelectorAll('.login-form').forEach(form => {
        form.classList.remove('active');
    });
    document.getElementById(`${tabName}-form`).classList.add('active');
}

// 处理密码登录表单提交
async function handlePasswordLogin(e) {
    e.preventDefault();

    const accountInput = document.getElementById('login-account');
    const passwordInput = document.getElementById('login-password');
    const rememberInput = document.getElementById('login-remember');
    const statusContainer = document.getElementById('password-login-status');

    if (!accountInput || !passwordInput) return;

    const account = accountInput.value.trim();
    const password = passwordInput.value;
    const captcha = document.getElementById('login-captcha').value.trim();
    const remember = rememberInput ? rememberInput.checked : false;

    if (!account || !password) {
        showStatus(statusContainer, '用户名/手机号和密码不能为空', 'error');
        return;
    }

    if (!captcha) {
        showStatus(statusContainer, '验证码不能为空', 'error');
        return;
    }

    try {
        showStatus(statusContainer, '正在登录...', 'info');

        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                account,
                password,
                captcha,
                remember
            })
        });

        const data = await response.json();

        if (data.success) {
            showStatus(statusContainer, '登录成功，正在跳转...', 'success');

            // 更新UI显示已登录状态
            updateAuthUI(data.data);

            // 关闭登录模态框
            setTimeout(() => {
                closeModal('login-modal');
                // 清空表单
                accountInput.value = '';
                passwordInput.value = '';
                document.getElementById('login-captcha').value = '';
                if (rememberInput) rememberInput.checked = false;
                statusContainer.style.display = 'none';
                // 刷新验证码
                refreshCaptcha();
            }, 1000);
        } else {
            showStatus(statusContainer, data.message || '登录失败', 'error');
            // 如果是验证码错误，刷新验证码
            if (data.message && data.message.includes('验证码')) {
                refreshCaptcha();
                document.getElementById('login-captcha').value = '';
            }
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        showStatus(statusContainer, '登录请求失败，请稍后再试', 'error');
    }
}

// 处理验证码登录表单提交
async function handleSmsLogin(e) {
    e.preventDefault();

    const phoneInput = document.getElementById('login-phone');
    const smsCodeInput = document.getElementById('login-sms-code');
    const statusContainer = document.getElementById('sms-login-status');

    if (!phoneInput || !smsCodeInput) return;

    const phone = phoneInput.value.trim();
    const smsCode = smsCodeInput.value.trim();

    if (!phone || !smsCode) {
        showStatus(statusContainer, '手机号和验证码不能为空', 'error');
        return;
    }

    if (!isValidPhone(phone)) {
        showStatus(statusContainer, '请输入有效的手机号码', 'error');
        return;
    }

    if (smsCode.length !== 6) {
        showStatus(statusContainer, '请输入6位验证码', 'error');
        return;
    }

    try {
        showStatus(statusContainer, '正在登录...', 'info');

        const response = await fetch('/api/auth/sms-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phone,
                sms_code: smsCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showStatus(statusContainer, '登录成功，正在跳转...', 'success');

            // 更新UI显示已登录状态
            updateAuthUI(data.data);

            // 关闭登录模态框
            setTimeout(() => {
                closeModal('login-modal');
                // 清空表单
                phoneInput.value = '';
                smsCodeInput.value = '';
                statusContainer.style.display = 'none';
            }, 1000);
        } else {
            showStatus(statusContainer, data.message || '登录失败', 'error');
        }
    } catch (error) {
        console.error('验证码登录请求失败:', error);
        showStatus(statusContainer, '登录请求失败，请稍后再试', 'error');
    }
}

// 发送验证码
async function handleSendSms(e) {
    e.preventDefault();

    const phoneInput = document.getElementById('login-phone');
    const sendSmsBtn = document.getElementById('send-sms-btn');
    const statusContainer = document.getElementById('sms-login-status');

    if (!phoneInput) return;

    const phone = phoneInput.value.trim();

    if (!phone) {
        showStatus(statusContainer, '请输入手机号', 'error');
        return;
    }

    if (!isValidPhone(phone)) {
        showStatus(statusContainer, '请输入有效的手机号码', 'error');
        return;
    }

    try {
        // 禁用按钮
        sendSmsBtn.disabled = true;
        sendSmsBtn.textContent = '发送中...';

        const response = await fetch('/api/auth/send-sms', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phone
            })
        });

        const data = await response.json();

        if (data.success) {
            showStatus(statusContainer, '验证码已发送，请查收', 'success');

            // 开始倒计时
            startSmsCountdown(sendSmsBtn);
        } else {
            showStatus(statusContainer, data.message || '发送失败', 'error');
            // 恢复按钮
            sendSmsBtn.disabled = false;
            sendSmsBtn.textContent = '发送验证码';
        }
    } catch (error) {
        console.error('发送验证码请求失败:', error);
        showStatus(statusContainer, '发送失败，请稍后再试', 'error');
        // 恢复按钮
        sendSmsBtn.disabled = false;
        sendSmsBtn.textContent = '发送验证码';
    }
}

// 验证码倒计时
function startSmsCountdown(button) {
    let countdown = 60;
    button.classList.add('countdown');

    const timer = setInterval(() => {
        button.textContent = `${countdown}秒后重发`;
        countdown--;

        if (countdown < 0) {
            clearInterval(timer);
            button.disabled = false;
            button.classList.remove('countdown');
            button.textContent = '发送验证码';
        }
    }, 1000);
}

// 手机号格式验证
function isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

// 处理注册表单提交
async function handleRegister(e) {
    e.preventDefault();

    const usernameInput = document.getElementById('register-username');
    const phoneInput = document.getElementById('register-phone');
    const passwordInput = document.getElementById('register-password');
    const confirmPasswordInput = document.getElementById('register-confirm-password');
    const smsCodeInput = document.getElementById('register-sms-code');
    const statusContainer = document.getElementById('register-status');

    if (!usernameInput || !phoneInput || !passwordInput || !confirmPasswordInput || !smsCodeInput) return;

    const username = usernameInput.value.trim();
    const phone = phoneInput.value.trim();
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    const smsCode = smsCodeInput.value.trim();

    // 基本验证
    if (!username || !phone || !password || !smsCode) {
        showStatus(statusContainer, '所有字段都是必填的', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showStatus(statusContainer, '两次输入的密码不一致', 'error');
        return;
    }

    if (password.length < 6) {
        showStatus(statusContainer, '密码长度至少为6个字符', 'error');
        return;
    }

    if (!isValidPhone(phone)) {
        showStatus(statusContainer, '请输入有效的手机号码', 'error');
        return;
    }

    try {
        showStatus(statusContainer, '正在注册...', 'info');

        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                phone,
                password,
                sms_code: smsCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showStatus(statusContainer, '注册成功，已自动登录', 'success');

            // 更新UI显示已登录状态
            updateAuthUI(data.data);

            // 关闭注册模态框
            setTimeout(() => {
                closeModal('register-modal');
                // 清空表单
                usernameInput.value = '';
                phoneInput.value = '';
                passwordInput.value = '';
                confirmPasswordInput.value = '';
                smsCodeInput.value = '';
                statusContainer.style.display = 'none';
            }, 1500);
        } else {
            showStatus(statusContainer, data.message || '注册失败', 'error');
        }
    } catch (error) {
        console.error('注册请求失败:', error);
        showStatus(statusContainer, '注册请求失败，请稍后再试', 'error');
    }
}

// 密码强度检测已移除 - 使用简化的密码校验

// 验证确认密码
function validateConfirmPassword() {
    const passwordInput = document.getElementById('register-password');
    const confirmPasswordInput = document.getElementById('register-confirm-password');

    if (!passwordInput || !confirmPasswordInput) return;

    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;

    if (confirmPassword && password !== confirmPassword) {
        confirmPasswordInput.setCustomValidity('两次输入的密码不一致');
    } else {
        confirmPasswordInput.setCustomValidity('');
    }
}

// 切换用户下拉菜单
function toggleUserDropdown(e) {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡
    const dropdown = document.querySelector('.dropdown-menu');
    if (dropdown) {
        dropdown.classList.toggle('show');

        // 添加鼠标离开事件监听器
        if (dropdown.classList.contains('show')) {
            // 当鼠标离开下拉菜单和用户菜单时，添加一个延迟，然后检查鼠标是否仍然在外面
            const userMenu = document.querySelector('.user-menu');

            // 移除之前可能存在的事件监听器
            document.removeEventListener('mouseover', handleMouseOver);

            // 添加新的事件监听器
            document.addEventListener('mouseover', handleMouseOver);
        }
    }
}

// 处理鼠标悬停事件
function handleMouseOver(e) {
    const dropdown = document.querySelector('.dropdown-menu');
    const userMenu = document.querySelector('.user-menu');

    if (dropdown && dropdown.classList.contains('show') && userMenu) {
        // 检查鼠标是否在下拉菜单或用户菜单上
        const isOverDropdown = dropdown.contains(e.target);
        const isOverUserMenu = userMenu.contains(e.target);

        // 如果鼠标不在下拉菜单或用户菜单上，则关闭下拉菜单
        if (!isOverDropdown && !isOverUserMenu) {
            // 不要立即关闭，添加一个小延迟，以便用户有时间移动到菜单上
            setTimeout(() => {
                // 再次检查鼠标位置
                const currentTarget = document.elementFromPoint(e.clientX, e.clientY);
                if (!dropdown.contains(currentTarget) && !userMenu.contains(currentTarget)) {
                    dropdown.classList.remove('show');
                }
            }, 100);
        }
    }
}

// 处理退出登录
async function handleLogout(e) {
    e.preventDefault();

    try {
        const response = await fetch('/api/auth/logout');
        const data = await response.json();

        if (data.success) {
            // 更新UI显示未登录状态
            updateAuthUI(null);
        } else {
            console.error('退出登录失败:', data.message);
        }
    } catch (error) {
        console.error('退出登录请求失败:', error);
    }
}

// 检查用户登录状态
async function checkAuthStatus() {
    try {
        const response = await fetch('/api/auth/check-auth');
        const data = await response.json();

        if (data.success) {
            // 用户已登录，更新UI
            updateAuthUI(data.data);
        } else {
            // 用户未登录，确保UI显示未登录状态
            updateAuthUI(null);
        }
    } catch (error) {
        console.error('检查登录状态失败:', error);
        // 出错时默认显示未登录状态
        updateAuthUI(null);
    }
}

// 更新认证UI
function updateAuthUI(userData) {
    const authButtons = document.getElementById('auth-buttons');
    const userMenu = document.getElementById('user-menu');
    const usernameDisplay = document.getElementById('username-display');

    // 工具区域元素
    const toolsLoginRequired = document.getElementById('tools-login-required');
    const toolContents = document.getElementById('tool-contents');
    const toolContentWrappers = document.querySelectorAll('.tool-content-wrapper');

    if (!authButtons || !userMenu) return;

    if (userData) {
        // 已登录状态
        window.isLoggedIn = true; // 设置全局登录状态变量
        authButtons.style.display = 'none';
        userMenu.style.display = 'flex';

        if (usernameDisplay) {
            usernameDisplay.textContent = userData.nickname || userData.username;
        }

        // 更新用户角色显示
        updateUserRoleDisplay(userData);

        // 如果是管理员，显示管理员入口
        const adminLink = document.getElementById('admin-link');
        if (adminLink) {
            adminLink.style.display = userData.is_admin ? 'block' : 'none';
        }

        // 显示工具内容，隐藏登录提示
        if (toolsLoginRequired) {
            toolsLoginRequired.style.display = 'none';
        }

        // 显示工具内容容器
        if (toolContents) {
            toolContents.style.display = 'block';
        }

        // 显示默认工具（抖音）
        const defaultTool = document.getElementById('douyin-tool-content');
        if (defaultTool) {
            defaultTool.classList.add('active');
            defaultTool.style.display = 'block';
        }

        // 确保其他工具隐藏
        toolContentWrappers.forEach(wrapper => {
            if (wrapper.id !== 'douyin-tool-content') {
                wrapper.classList.remove('active');
                wrapper.style.display = 'none';
            }
        });
    } else {
        // 未登录状态
        window.isLoggedIn = false; // 设置全局登录状态变量
        authButtons.style.display = 'flex';
        userMenu.style.display = 'none';

        // 隐藏工具内容，显示登录提示
        if (toolsLoginRequired) {
            toolsLoginRequired.style.display = 'block';
        }

        // 隐藏整个工具内容容器
        if (toolContents) {
            toolContents.style.display = 'none';
        }

        // 隐藏所有工具内容
        toolContentWrappers.forEach(wrapper => {
            wrapper.style.display = 'none';
        });
    }

    // 延迟强制更新，确保状态正确应用
    setTimeout(() => {
        forceUpdateToolsVisibility();
        // 如果用户已登录，触发权限系统更新
        if (userData && typeof window.PermissionManager !== 'undefined') {
            window.PermissionManager.loadUserPermissions();
        }
    }, 50);
}

// 更新用户角色显示
function updateUserRoleDisplay(userData) {
    const userRoleElement = document.getElementById('user-role');
    if (!userRoleElement || !userData) return;

    // 根据用户数据确定角色显示
    let roleDisplay = '普通用户';
    let roleClass = 'role-normal';

    if (userData.is_admin) {
        roleDisplay = '超级管理员';
        roleClass = 'role-admin';
    } else if (userData.role === 'pro_user') {
        roleDisplay = 'Pro用户';
        roleClass = 'role-pro';
    } else if (userData.role === 'normal_user') {
        roleDisplay = '普通用户';
        roleClass = 'role-normal';
    }

    // 更新角色显示
    userRoleElement.textContent = roleDisplay;
    userRoleElement.className = `user-role ${roleClass}`;

    // 如果是Pro用户，显示到期时间
    const vipExpireElement = document.getElementById('vip-expire');
    if (vipExpireElement) {
        if (userData.role === 'pro_user' && userData.vip_expire_date) {
            const expireDate = new Date(userData.vip_expire_date);
            vipExpireElement.textContent = `到期时间: ${expireDate.toLocaleDateString()}`;
            vipExpireElement.style.display = 'block';
        } else {
            vipExpireElement.style.display = 'none';
        }
    }
}

/**
 * 强制更新工具区域可见性
 */
function forceUpdateToolsVisibility() {
    const toolsLoginRequired = document.getElementById('tools-login-required');
    const toolContents = document.getElementById('tool-contents');
    const toolContentWrappers = document.querySelectorAll('.tool-content-wrapper');

    console.log('强制更新工具区域可见性，当前登录状态:', window.isLoggedIn);

    if (window.isLoggedIn) {
        // 已登录：隐藏登录提示，显示工具内容
        if (toolsLoginRequired) {
            toolsLoginRequired.style.display = 'none';
        }
        if (toolContents) {
            toolContents.style.display = 'block';
        }

        // 显示默认工具（抖音）
        const defaultTool = document.getElementById('douyin-tool-content');
        if (defaultTool) {
            defaultTool.classList.add('active');
            defaultTool.style.display = 'block';
        }

        // 确保其他工具隐藏
        toolContentWrappers.forEach(wrapper => {
            if (wrapper.id !== 'douyin-tool-content') {
                wrapper.classList.remove('active');
                wrapper.style.display = 'none';
            }
        });

        console.log('已登录用户：显示工具内容');
    } else {
        // 未登录：显示登录提示，隐藏工具内容
        if (toolsLoginRequired) {
            toolsLoginRequired.style.display = 'block';
        }
        if (toolContents) {
            toolContents.style.display = 'none';
        }
        // 同时隐藏所有工具包装器
        toolContentWrappers.forEach(wrapper => {
            wrapper.style.display = 'none';
            wrapper.classList.remove('active');
        });
        console.log('未登录用户：隐藏工具内容，显示登录提示');
    }
}

// 显示状态消息
function showStatus(container, message, type) {
    if (!container) return;

    container.style.display = 'block';

    const messageEl = container.querySelector('.status-message');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = 'status-message ' + (type || '');
    }
}

// 验证手机号格式
function isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
}

// 加载用户个人资料
async function loadUserProfile() {
    const profileUsername = document.getElementById('profile-username');
    const profilePhone = document.getElementById('profile-phone');
    const profileCreatedAt = document.getElementById('profile-created-at');
    const profileLastLogin = document.getElementById('profile-last-login');

    if (!profileUsername || !profilePhone || !profileCreatedAt || !profileLastLogin) return;

    try {
        const response = await fetch('/api/auth/profile');
        const data = await response.json();

        if (data.success) {
            const profile = data.data;
            profileUsername.textContent = profile.username;
            profilePhone.textContent = profile.phone || '-';
            profileCreatedAt.textContent = formatDate(profile.created_at);
            profileLastLogin.textContent = formatDate(profile.last_login);
        } else {
            console.error('获取个人资料失败:', data.message);
        }
    } catch (error) {
        console.error('获取个人资料请求失败:', error);
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 加载下载历史记录
async function loadDownloadHistory() {
    // 实现下载历史记录加载逻辑
    // 这部分将在下载历史模态框实现后添加
}

// 导出函数到全局作用域，确保可以从其他脚本访问
window.initAuthFeatures = initAuthFeatures;
window.openModal = openModal;
window.closeModal = closeModal;
window.closeAllModals = closeAllModals;
window.showLoginModal = showLoginModal;
window.showRegisterModal = showRegisterModal;
window.checkAuthStatus = checkAuthStatus;
window.forceUpdateToolsVisibility = forceUpdateToolsVisibility;

// 刷新验证码
async function refreshCaptcha() {
    try {
        const response = await fetch('/api/auth/captcha');
        const data = await response.json();

        if (data.success) {
            const captchaImage = document.getElementById('captcha-image');
            if (captchaImage) {
                captchaImage.src = data.data.image;
            }
        } else {
            console.error('获取验证码失败:', data.message);
        }
    } catch (error) {
        console.error('获取验证码请求失败:', error);
    }
}

// 显示登录模态框
function showLoginModal() {
    openModal('login-modal');
}

// 显示注册模态框
function showRegisterModal() {
    openModal('register-modal');
}

// 导出验证码函数到全局作用域
window.refreshCaptcha = refreshCaptcha;

// 页面加载完成后立即检查并强制更新工具可见性
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保所有元素都已加载
    setTimeout(() => {
        console.log('页面加载完成，检查工具可见性...');
        if (typeof window.isLoggedIn === 'undefined') {
            window.isLoggedIn = false; // 默认未登录
        }
        forceUpdateToolsVisibility();
    }, 200);
});

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Flask主应用程序
整合所有API路由
"""

import os
import logging
import sys
from pathlib import Path

from flask import Flask, jsonify, render_template, send_from_directory, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user
import pymysql

# 使用 PyMySQL 替代 MySQLdb
pymysql.install_as_MySQLdb()

# 配置数据库
db = SQLAlchemy()

# 配置登录管理器
login_manager = LoginManager()
login_manager.session_protection = 'strong'
login_manager.login_view = None  # API应用不需要重定向到登录页面

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(Path(__file__).resolve().parent.parent, 'logs', 'app.log'),
                           encoding='utf-8', mode='a')
    ]
)
logger = logging.getLogger("superspider")

def create_app():
    """创建Flask应用"""
    app = Flask(__name__,
                static_folder='../frontend/static',
                template_folder='../frontend/templates')

    # 检测是否为重载进程
    is_reloader = os.environ.get('WERKZEUG_RUN_MAIN') == 'true'

    # 配置应用
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev_key')
    app.config['JSON_AS_ASCII'] = False
    app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 限制最大请求大小为50MB

    # 设置开发环境标识
    app.config['DEBUG'] = True
    os.environ['DEBUG'] = 'True'
    os.environ['FLASK_ENV'] = 'development'

    # 配置数据库 - MySQL
    # 从环境变量获取数据库配置，如果没有则使用默认值
    db_user = os.environ.get('MYSQL_USER', 'root')
    db_password = os.environ.get('MYSQL_PASSWORD', '123456')
    db_host = os.environ.get('MYSQL_HOST', 'localhost')
    db_port = os.environ.get('MYSQL_PORT', '3306')
    db_name = os.environ.get('MYSQL_DATABASE', 'superspider')

    # 构建 MySQL 连接字符串
    app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_POOL_RECYCLE'] = 280  # 防止连接超时

    # 初始化数据库
    db.init_app(app)

    # 初始化登录管理器
    login_manager.init_app(app)

    # 用户加载函数
    from backend.models.user import User
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # 自定义未授权处理器
    @login_manager.unauthorized_handler
    def unauthorized():
        return jsonify({
            "success": False,
            "message": "需要登录才能访问此资源",
            "data": None
        }), 401

    # 导入所有模型以确保表被创建
    from backend.models.activation_code import ActivationCode
    from backend.models.search_record import SearchRecord
    from backend.models.vip_account import VipAccount
    from backend.models.user_usage import UserUsage

    # 创建所有表
    with app.app_context():
        db.create_all()

        # 初始化定时任务调度器
        try:
            from backend.utils.scheduler import init_scheduler
            init_scheduler()
            logger.info("定时任务调度器初始化成功")
        except Exception as e:
            logger.error(f"定时任务调度器初始化失败: {e}")

    # 注册错误处理
    @app.errorhandler(404)
    def page_not_found(e):
        return jsonify({
            "success": False,
            "message": "API接口不存在",
            "data": None
        }), 404

    @app.errorhandler(500)
    def server_error(e):
        return jsonify({
            "success": False,
            "message": "服务器内部错误",
            "data": None
        }), 500

    # 注册API路由
    # 导入API蓝图
    try:
        from .api.kuaishou_api import kuaishou_api
        app.register_blueprint(kuaishou_api, url_prefix='/api/kuaishou')
        if not is_reloader:
            logger.info("已注册快手API路由到 /api/kuaishou")
    except Exception as e:
        logger.error(f"注册快手API路由失败: {e}")

    try:
        from .api.douyin_api import douyin_api
        app.register_blueprint(douyin_api, url_prefix='/api/douyin')
        if not is_reloader:
            logger.info("已注册抖音API路由到 /api/douyin")
    except Exception as e:
        logger.error(f"注册抖音API路由失败: {e}")

    try:
        from .api.bilibili_api import bilibili_api
        app.register_blueprint(bilibili_api, url_prefix='/api/bilibili')
        if not is_reloader:
            logger.info("已注册哔哩哔哩API路由到 /api/bilibili")
    except Exception as e:
        logger.error(f"注册哔哩哔哩API路由失败: {e}")

    try:
        from .api.csdn_api import csdn_api
        app.register_blueprint(csdn_api, url_prefix='/api/csdn')
        if not is_reloader:
            logger.info("已注册CSDN API路由到 /api/csdn")
    except Exception as e:
        logger.error(f"注册CSDN API路由失败: {e}")

    try:
        from .api.auth_api import auth_api
        app.register_blueprint(auth_api, url_prefix='/api/auth')
        if not is_reloader:
            logger.info("已注册认证API路由到 /api/auth")
    except Exception as e:
        logger.error(f"注册认证API路由失败: {e}")

    try:
        from .api.search_api import search_api
        app.register_blueprint(search_api, url_prefix='/api/search')
        if not is_reloader:
            logger.info("已注册搜索历史API路由到 /api/search")
    except Exception as e:
        logger.error(f"注册搜索历史API路由失败: {e}")

    try:
        from .api.admin_api import admin_api
        app.register_blueprint(admin_api, url_prefix='/api/admin')
        if not is_reloader:
            logger.info("已注册管理员API路由到 /api/admin")
    except Exception as e:
        logger.error(f"注册管理员API路由失败: {e}")

    try:
        from .api.permission_api import permission_api
        app.register_blueprint(permission_api, url_prefix='/api/permission')
        if not is_reloader:
            logger.info("已注册权限管理API路由到 /api/permission")
    except Exception as e:
        logger.error(f"注册权限管理API路由失败: {e}")

    try:
        from .api.activation_api import activation_api
        app.register_blueprint(activation_api, url_prefix='/api/activation')
        if not is_reloader:
            logger.info("已注册激活码API路由到 /api/activation")
    except Exception as e:
        logger.error(f"注册激活码API路由失败: {e}")

    try:
        from .api.vip_account_api import vip_account_api
        app.register_blueprint(vip_account_api, url_prefix='/api')
        if not is_reloader:
            logger.info("已注册VIP账号管理API路由到 /api/vip-accounts")
    except Exception as e:
        logger.error(f"注册VIP账号管理API路由失败: {e}")

    try:
        from .api.usage_api import usage_api
        app.register_blueprint(usage_api, url_prefix='/api/usage')
        if not is_reloader:
            logger.info("已注册使用统计API路由到 /api/usage")
    except Exception as e:
        logger.error(f"注册使用统计API路由失败: {e}")

    try:
        from .api.idphoto_api import idphoto_api
        app.register_blueprint(idphoto_api, url_prefix='/api/idphoto')
        if not is_reloader:
            logger.info("已注册证件照API路由到 /api/idphoto")
    except Exception as e:
        logger.error(f"注册证件照API路由失败: {e}")

    # 注册首页路由
    @app.route('/')
    def index():
        return render_template('index.html')

    # VIP账号管理页面路由
    @app.route('/vip-accounts')
    @login_required
    def vip_accounts():
        # 检查管理员权限
        if not current_user.has_permission('admin', 'user_management'):
            return redirect(url_for('index'))
        return render_template('vip_accounts.html')

    # 媒体文件服务路由
    @app.route('/media/videos/<filename>')
    def serve_video(filename):
        """提供视频文件服务"""
        try:
            # 视频文件存储在 spiders/data 目录
            video_dir = os.path.join(os.path.dirname(__file__), 'spiders', 'data')
            return send_from_directory(video_dir, filename)
        except Exception as e:
            logger.error(f"提供视频文件失败: {e}")
            return jsonify({
                "success": False,
                "message": "视频文件不存在",
                "data": None
            }), 404

    # 检查系统状态路由
    @app.route('/api/status')
    def api_status():
        return jsonify({
            "success": True,
            "message": "服务正常",
            "data": {
                "version": "1.0.0",
                "apis": [
                    {
                        "name": "kuaishou",
                        "url": "/api/kuaishou",
                        "status": "active"
                    },
                    {
                        "name": "douyin",
                        "url": "/api/douyin",
                        "status": "active"
                    },
                    {
                        "name": "bilibili",
                        "url": "/api/bilibili",
                        "status": "active"
                    },
                    {
                        "name": "csdn",
                        "url": "/api/csdn",
                        "status": "active"
                    },
                    {
                        "name": "auth",
                        "url": "/api/auth",
                        "status": "active"
                    }
                ]
            }
        })

    return app

# 应用实例
app = create_app()
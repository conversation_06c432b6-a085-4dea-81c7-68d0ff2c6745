#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试证件照API请求
模拟前端请求，排查问题
"""

import requests
import json
import time
from PIL import Image
import io
import base64

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (300, 400), color=(255, 255, 255))
    
    # 添加一些简单的内容
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.rectangle([50, 50, 250, 350], fill=(200, 200, 200))
    draw.ellipse([100, 100, 200, 200], fill=(150, 150, 150))  # 模拟人脸
    
    # 转换为字节流
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_hivision_direct():
    """直接测试HivisionIDPhotos API"""
    print("🔍 测试HivisionIDPhotos API...")
    
    try:
        # 创建测试图片
        img_bytes = create_test_image()
        
        # 准备请求
        files = {'input_image': ('test.jpg', img_bytes, 'image/jpeg')}
        data = {
            'height': 413,
            'width': 295,
            'human_matting_model': 'modnet_photographic_portrait_matting',
            'face_detect_model': 'mtcnn',
            'hd': True,
            'dpi': 300,
            'face_align': True
        }
        
        print("📤 发送请求到HivisionIDPhotos...")
        response = requests.post('http://127.0.0.1:8080/idphoto', files=files, data=data, timeout=60)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ HivisionIDPhotos API调用成功!")
            print(f"   返回字段: {list(result.keys())}")
            
            if result.get('status'):
                print("✅ 图片处理成功")
                if 'image_base64_standard' in result:
                    print(f"   标准图片长度: {len(result['image_base64_standard'])} 字符")
                if 'image_base64_hd' in result:
                    print(f"   高清图片长度: {len(result['image_base64_hd'])} 字符")
            else:
                print("❌ 图片处理失败")
                print(f"   结果: {result}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_superspider_api():
    """测试SuperSpider的证件照API"""
    print("\n🔍 测试SuperSpider证件照API...")

    try:
        # 创建测试图片
        img_bytes = create_test_image()

        # 模拟前端请求
        files = {'photo': ('test.jpg', img_bytes, 'image/jpeg')}
        data = {
            'size': '1inch',
            'background': 'blue'
        }

        # 创建会话以处理登录
        session = requests.Session()

        # 先尝试登录（如果需要）
        login_data = {
            'username': 'test',
            'password': 'test123'
        }
        login_response = session.post('http://127.0.0.1:5000/auth/login', data=login_data)
        print(f"🔐 登录状态: {login_response.status_code}")

        print("📤 发送请求到SuperSpider...")
        response = session.post('http://127.0.0.1:5000/api/idphoto/create', files=files, data=data, timeout=60)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SuperSpider API调用成功!")
            print(f"   返回字段: {list(result.keys())}")
            
            if result.get('success'):
                print("✅ 证件照制作成功")
                data = result.get('data', {})
                if 'single_photo' in data:
                    print(f"   单张照片长度: {len(data['single_photo'])} 字符")
                if 'layout_photo' in data:
                    print(f"   排版照片长度: {len(data['layout_photo'])} 字符")
                print(f"   尺寸信息: {data.get('size_info', 'N/A')}")
            else:
                print("❌ 证件照制作失败")
                print(f"   错误信息: {result.get('message', 'Unknown error')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   错误信息: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_api_status():
    """测试API状态"""
    print("\n🔍 测试API状态...")
    
    # 测试HivisionIDPhotos状态
    try:
        response = requests.get('http://127.0.0.1:8080/docs', timeout=5)
        print(f"✅ HivisionIDPhotos状态: {response.status_code}")
    except Exception as e:
        print(f"❌ HivisionIDPhotos连接失败: {e}")
    
    # 测试SuperSpider状态
    try:
        response = requests.get('http://127.0.0.1:5000/api/idphoto/status', timeout=5)
        print(f"✅ SuperSpider状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   详细状态: {result}")
    except Exception as e:
        print(f"❌ SuperSpider连接失败: {e}")

if __name__ == '__main__':
    print("🚀 开始测试证件照API...")
    
    # 测试API状态
    test_api_status()
    
    # 测试HivisionIDPhotos直接调用
    test_hivision_direct()
    
    # 测试SuperSpider API
    test_superspider_api()
    
    print("\n✅ 测试完成!")

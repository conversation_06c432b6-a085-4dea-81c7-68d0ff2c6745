#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模拟浏览器请求测试证件照API
"""

import requests
import os

def test_browser_like_request():
    """模拟浏览器请求"""
    print("🔍 模拟浏览器请求测试...")
    
    # 创建会话
    session = requests.Session()
    
    # 设置浏览器头部
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'http://127.0.0.1:5000/',
        'Origin': 'http://127.0.0.1:5000'
    })
    
    try:
        # 1. 先访问主页获取session
        print("📤 访问主页...")
        home_response = session.get('http://127.0.0.1:5000/')
        print(f"📥 主页状态: {home_response.status_code}")
        
        # 2. 检查是否需要登录
        print("📤 检查认证状态...")
        auth_response = session.get('http://127.0.0.1:5000/api/auth/check-auth')
        print(f"📥 认证状态: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            print(f"   认证信息: {auth_data}")
            
            if not auth_data.get('authenticated', False):
                print("🔐 需要登录，尝试登录...")
                
                # 尝试登录
                login_data = {
                    'username': 'test',
                    'password': 'test123'
                }
                
                login_response = session.post('http://127.0.0.1:5000/api/auth/login', 
                                            json=login_data)
                print(f"📥 登录状态: {login_response.status_code}")
                
                if login_response.status_code == 200:
                    login_result = login_response.json()
                    print(f"   登录结果: {login_result}")
                else:
                    print(f"   登录失败: {login_response.text}")
        
        # 3. 测试证件照API
        print("📤 测试证件照API...")
        
        # 查找测试图片
        test_image = "SuperSpider/backend/utils/HivisionIDPhotos-master/demo/images/test0.jpg"
        if not os.path.exists(test_image):
            print("❌ 测试图片不存在")
            return
        
        # 准备文件和数据
        with open(test_image, 'rb') as f:
            files = {'photo': ('test0.jpg', f, 'image/jpeg')}
            data = {
                'size': '1inch',
                'background': 'blue'
            }
            
            # 发送请求
            response = session.post('http://127.0.0.1:5000/api/idphoto/create', 
                                  files=files, data=data, timeout=120)
            
            print(f"📥 证件照API状态: {response.status_code}")
            print(f"📥 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print("✅ 证件照制作成功!")
                    print(f"   返回字段: {list(result.keys())}")
                    
                    if result.get('success'):
                        data = result.get('data', {})
                        print(f"   尺寸信息: {data.get('size_info', 'N/A')}")
                        if 'single_photo' in data:
                            print(f"   单张照片长度: {len(data['single_photo'])} 字符")
                        if 'layout_photo' in data:
                            print(f"   排版照片长度: {len(data['layout_photo'])} 字符")
                    else:
                        print(f"❌ 制作失败: {result.get('message', 'Unknown error')}")
                        
                except Exception as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"   原始响应: {response.text[:500]}...")
            else:
                print(f"❌ 请求失败")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data}")
                except:
                    print(f"   错误内容: {response.text[:500]}...")
                    
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_direct_hivision():
    """直接测试HivisionIDPhotos"""
    print("\n🔍 直接测试HivisionIDPhotos...")
    
    test_image = "SuperSpider/backend/utils/HivisionIDPhotos-master/demo/images/test0.jpg"
    if not os.path.exists(test_image):
        print("❌ 测试图片不存在")
        return
    
    try:
        with open(test_image, 'rb') as f:
            files = {'input_image': f}
            data = {
                'height': 413,
                'width': 295,
                'face_align': True
            }
            
            response = requests.post('http://127.0.0.1:8080/idphoto', 
                                   files=files, data=data, timeout=60)
            
            print(f"📥 HivisionIDPhotos状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ HivisionIDPhotos成功: {result.get('status', False)}")
                if result.get('status'):
                    print(f"   标准图片长度: {len(result.get('image_base64_standard', ''))}")
            else:
                print(f"❌ HivisionIDPhotos失败: {response.text[:200]}...")
                
    except Exception as e:
        print(f"❌ HivisionIDPhotos测试异常: {e}")

if __name__ == '__main__':
    print("🚀 开始浏览器模拟测试...")
    
    test_direct_hivision()
    test_browser_like_request()
    
    print("\n✅ 测试完成!")

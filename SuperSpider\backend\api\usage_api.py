#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户使用统计API模块
提供用户使用情况查询和统计相关的API
"""

import logging
import datetime
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

from backend.models.user_usage import UserUsage
from backend.utils.permissions import require_role
from backend.main import db

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
usage_api = Blueprint('usage_api', __name__)

@usage_api.route('/today', methods=['GET'])
@login_required
def get_today_usage():
    """
    获取今日使用情况

    响应:
        {
            "success": true,
            "message": "获取今日使用情况成功",
            "data": {
                "download_count": 2,
                "api_call_count": 8,
                "remaining_downloads": 3,
                "remaining_api_calls": 2,
                "limits": {
                    "download_limit": 5,
                    "api_rate_limit": 3
                },
                "reset_time": "2024-01-02T00:00:00"
            }
        }
    """
    try:
        # 获取今日使用记录
        usage = UserUsage.get_or_create_today_usage(current_user.id)
        
        # 获取用户权限
        permissions = current_user.get_permissions()
        download_limit = permissions.get('download_limit', 5)
        api_rate_limit = permissions.get('api_rate_limit', 3)
        
        # 计算剩余次数
        remaining_downloads = usage.get_remaining_downloads(current_user)
        remaining_api_calls = usage.get_remaining_api_calls_this_minute(current_user)
        
        # 计算下次重置时间（明天凌晨）
        tomorrow = datetime.date.today() + datetime.timedelta(days=1)
        reset_time = datetime.datetime.combine(tomorrow, datetime.time(0, 0))
        
        return jsonify({
            "success": True,
            "message": "获取今日使用情况成功",
            "data": {
                "download_count": usage.download_count,
                "api_call_count": usage.api_call_count,
                "api_calls_this_minute": usage.api_calls_this_minute,
                "remaining_downloads": remaining_downloads,
                "remaining_api_calls": remaining_api_calls,
                "limits": {
                    "download_limit": download_limit,
                    "api_rate_limit": api_rate_limit
                },
                "date": usage.date.isoformat(),
                "reset_time": reset_time.isoformat(),
                "last_api_call": usage.last_api_call.isoformat() if usage.last_api_call else None
            }
        })

    except Exception as e:
        logger.error(f"获取今日使用情况失败: {e}")
        return jsonify({
            "success": False,
            "message": "获取今日使用情况失败",
            "data": None
        }), 500

@usage_api.route('/history', methods=['GET'])
@login_required
def get_usage_history():
    """
    获取使用历史

    查询参数:
        days: 查询天数，默认7天

    响应:
        {
            "success": true,
            "message": "获取使用历史成功",
            "data": {
                "total_downloads": 15,
                "total_api_calls": 89,
                "days": 7,
                "daily_records": [
                    {
                        "date": "2024-01-01",
                        "downloads": 5,
                        "api_calls": 23
                    }
                ]
            }
        }
    """
    try:
        days = request.args.get('days', 7, type=int)
        if days <= 0 or days > 30:
            days = 7

        # 获取用户统计
        stats = UserUsage.get_user_stats(current_user.id, days)

        return jsonify({
            "success": True,
            "message": "获取使用历史成功",
            "data": stats
        })

    except Exception as e:
        logger.error(f"获取使用历史失败: {e}")
        return jsonify({
            "success": False,
            "message": "获取使用历史失败",
            "data": None
        }), 500

@usage_api.route('/stats/summary', methods=['GET'])
@login_required
def get_usage_summary():
    """
    获取使用情况摘要（用于前端显示）

    响应:
        {
            "success": true,
            "message": "获取摘要成功",
            "data": {
                "today": {
                    "downloads": "2/5",
                    "api_calls": "8次",
                    "api_calls_this_minute": "2/3"
                },
                "this_week": {
                    "total_downloads": 15,
                    "total_api_calls": 89
                },
                "status": "normal",  // normal, warning, limit_reached
                "next_reset": "2024-01-02T00:00:00"
            }
        }
    """
    try:
        # 获取今日使用情况
        usage = UserUsage.get_or_create_today_usage(current_user.id)
        permissions = current_user.get_permissions()
        
        download_limit = permissions.get('download_limit', 5)
        api_rate_limit = permissions.get('api_rate_limit', 3)
        
        remaining_downloads = usage.get_remaining_downloads(current_user)
        remaining_api_calls = usage.get_remaining_api_calls_this_minute(current_user)
        
        # 获取本周统计
        week_stats = UserUsage.get_user_stats(current_user.id, 7)
        
        # 判断状态
        status = "normal"
        if remaining_downloads == 0 or remaining_api_calls == 0:
            status = "limit_reached"
        elif (download_limit > 0 and usage.download_count >= download_limit * 0.8) or \
             (api_rate_limit > 0 and usage.api_calls_this_minute >= api_rate_limit * 0.8):
            status = "warning"
        
        # 下次重置时间
        tomorrow = datetime.date.today() + datetime.timedelta(days=1)
        next_reset = datetime.datetime.combine(tomorrow, datetime.time(0, 0))
        
        return jsonify({
            "success": True,
            "message": "获取摘要成功",
            "data": {
                "today": {
                    "downloads": f"{usage.download_count}/{download_limit}" if download_limit != -1 else f"{usage.download_count}/无限制",
                    "api_calls": f"{usage.api_call_count}次",
                    "api_calls_this_minute": f"{usage.api_calls_this_minute}/{api_rate_limit}" if api_rate_limit != -1 else f"{usage.api_calls_this_minute}/无限制"
                },
                "this_week": {
                    "total_downloads": week_stats['total_downloads'],
                    "total_api_calls": week_stats['total_api_calls']
                },
                "status": status,
                "next_reset": next_reset.isoformat(),
                "remaining": {
                    "downloads": remaining_downloads,
                    "api_calls": remaining_api_calls
                }
            }
        })

    except Exception as e:
        logger.error(f"获取使用摘要失败: {e}")
        return jsonify({
            "success": False,
            "message": "获取使用摘要失败",
            "data": None
        }), 500

@usage_api.route('/reset', methods=['POST'])
@require_role('super_admin')
def reset_user_usage():
    """
    重置用户使用统计（仅超级管理员）

    请求体:
        {
            "user_id": 1,  // 可选，不提供则重置所有用户
            "reset_type": "today"  // "today" 或 "all"
        }

    响应:
        {
            "success": true,
            "message": "重置成功",
            "data": {
                "affected_records": 5
            }
        }
    """
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        reset_type = data.get('reset_type', 'today')
        
        if reset_type == 'today':
            # 重置今日使用统计
            today = datetime.date.today()
            query = UserUsage.query.filter_by(date=today)
            
            if user_id:
                query = query.filter_by(user_id=user_id)
            
            records = query.all()
            for record in records:
                record.download_count = 0
                record.api_call_count = 0
                record.api_calls_this_minute = 0
            
            db.session.commit()
            
        elif reset_type == 'all':
            # 删除所有历史记录
            query = UserUsage.query
            
            if user_id:
                query = query.filter_by(user_id=user_id)
            
            affected_records = query.count()
            query.delete()
            db.session.commit()
            
            return jsonify({
                "success": True,
                "message": "重置成功",
                "data": {
                    "affected_records": affected_records
                }
            })
        
        return jsonify({
            "success": True,
            "message": "重置成功",
            "data": {
                "affected_records": len(records) if reset_type == 'today' else 0
            }
        })

    except Exception as e:
        logger.error(f"重置用户使用统计失败: {e}")
        return jsonify({
            "success": False,
            "message": "重置失败",
            "data": None
        }), 500

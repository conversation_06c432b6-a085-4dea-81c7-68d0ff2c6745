#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户认证API蓝图
处理用户登录、注册、退出等请求
"""

import logging
from flask import Blueprint, request, jsonify, session
from flask_login import login_user, logout_user, login_required, current_user

from backend.main import db
from backend.models.user import User
from backend.utils.captcha import generate_login_captcha

# 配置日志
logger = logging.getLogger("superspider.auth")

# 创建蓝图
auth_api = Blueprint('auth_api', __name__)

@auth_api.route('/captcha', methods=['GET'])
def get_captcha():
    """生成登录验证码"""
    try:
        # 生成验证码
        code, image_data = generate_login_captcha()

        # 将验证码存储到session中
        session['login_captcha'] = code.upper()

        return jsonify({
            "success": True,
            "message": "验证码生成成功",
            "data": {
                "image": image_data
            }
        })
    except Exception as e:
        logger.error(f"生成验证码异常: {e}")
        return jsonify({
            "success": False,
            "message": "验证码生成失败",
            "data": None
        }), 500

@auth_api.route('/register', methods=['POST'])
def register():
    """用户注册处理"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    username = data.get('username', '').strip()
    phone = data.get('phone', '').strip()
    password = data.get('password', '')
    sms_code = data.get('sms_code', '').strip()

    # 参数验证
    if not username or not phone or not password or not sms_code:
        return jsonify({
            "success": False,
            "message": "用户名、手机号、密码和验证码不能为空",
            "data": None
        }), 400

    # 服务端用户名校验
    username_validation = validate_username_server(username)
    if not username_validation['valid']:
        return jsonify({
            "success": False,
            "message": username_validation['message'],
            "data": None
        }), 400

    # 服务端手机号校验
    phone_validation = validate_phone_server(phone)
    if not phone_validation['valid']:
        return jsonify({
            "success": False,
            "message": phone_validation['message'],
            "data": None
        }), 400

    # 检查用户名是否存在
    if User.query.filter_by(username=username).first():
        return jsonify({
            "success": False,
            "message": "用户名已被占用",
            "data": None
        }), 400

    # 检查手机号是否存在
    if User.query.filter_by(phone=phone).first():
        return jsonify({
            "success": False,
            "message": "手机号已被注册",
            "data": None
        }), 400

    # 密码强度检查
    password_validation = validate_password_server(password)
    if not password_validation['valid']:
        return jsonify({
            "success": False,
            "message": password_validation['message'],
            "data": None
        }), 400

    # 验证短信验证码
    try:
        from flask import session
        stored_code = session.get(f'register_sms_code_{phone}')
        stored_time = session.get(f'register_sms_time_{phone}')

        if not stored_code or not stored_time:
            return jsonify({
                "success": False,
                "message": "验证码已过期，请重新获取",
                "data": None
            }), 400

        # 检查验证码是否过期（5分钟）
        import time
        if time.time() - stored_time > 300:
            return jsonify({
                "success": False,
                "message": "验证码已过期，请重新获取",
                "data": None
            }), 400

        # 验证验证码
        if stored_code != sms_code:
            return jsonify({
                "success": False,
                "message": "验证码错误",
                "data": None
            }), 400

    except Exception as e:
        logger.error(f"验证码验证异常: {e}")
        return jsonify({
            "success": False,
            "message": "验证码验证失败，请稍后再试",
            "data": None
        }), 500

    # 创建新用户
    try:
        user = User(
            username=username,
            phone=phone,
            password=password
        )
        db.session.add(user)
        db.session.commit()

        # 清除验证码
        session.pop(f'register_sms_code_{phone}', None)
        session.pop(f'register_sms_time_{phone}', None)

        # 注册成功后自动登录
        login_user(user)

        return jsonify({
            "success": True,
            "message": "注册成功",
            "data": {
                "user_id": user.id,
                "username": user.username,
                "nickname": user.username,  # 昵称，暂时使用用户名
                "role": user.role,
                "role_display": user.get_role_display(),
                "is_admin": user.is_admin,
                "vip_expire_date": user.vip_expire_date.isoformat() if user.vip_expire_date else None,
                "is_pro_valid": user.is_pro_user_valid()
            }
        }), 201
    except Exception as e:
        logger.error(f"注册异常: {e}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": "注册失败，请稍后再试",
            "data": None
        }), 500

@auth_api.route('/login', methods=['POST'])
def login():
    """用户登录处理"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    account = data.get('account', '').strip()  # 用户名或手机号
    password = data.get('password', '')
    captcha = data.get('captcha', '').strip().upper()  # 验证码
    remember = data.get('remember', False)

    # 参数验证
    if not account or not password:
        return jsonify({
            "success": False,
            "message": "用户名/手机号和密码不能为空",
            "data": None
        }), 400

    if not captcha:
        return jsonify({
            "success": False,
            "message": "验证码不能为空",
            "data": None
        }), 400

    # 验证验证码
    session_captcha = session.get('login_captcha', '').upper()
    if not session_captcha or captcha != session_captcha:
        # 清除验证码
        session.pop('login_captcha', None)
        return jsonify({
            "success": False,
            "message": "验证码错误",
            "data": None
        }), 400

    # 根据用户名或手机号查找用户
    user = None
    if account.isdigit() and len(account) == 11:
        # 如果是11位数字，按手机号查找
        user = User.query.filter_by(phone=account).first()
    else:
        # 否则按用户名查找
        user = User.query.filter_by(username=account).first()

    # 验证用户和密码
    if user and user.verify_password(password):
        # 更新登录信息
        user.update_login_info()
        db.session.commit()

        login_user(user, remember=remember)

        # 清除验证码
        session.pop('login_captcha', None)

        return jsonify({
            "success": True,
            "message": "登录成功",
            "data": {
                "user_id": user.id,
                "username": user.username,
                "nickname": user.username,  # 昵称，暂时使用用户名
                "role": user.role,
                "role_display": user.get_role_display(),
                "is_admin": user.is_admin,
                "vip_expire_date": user.vip_expire_date.isoformat() if user.vip_expire_date else None,
                "is_pro_valid": user.is_pro_user_valid()
            }
        })

    return jsonify({
        "success": False,
        "message": "用户名/手机号或密码错误",
        "data": None
    }), 401

@auth_api.route('/logout')
@login_required
def logout():
    """用户退出处理"""
    logout_user()
    return jsonify({
        "success": True,
        "message": "退出成功",
        "data": None
    })

@auth_api.route('/profile', methods=['GET'])
@login_required
def get_profile():
    """获取当前用户资料"""
    return jsonify({
        "success": True,
        "message": "获取成功",
        "data": current_user.to_dict(include_sensitive=True)
    })

@auth_api.route('/profile', methods=['PUT'])
@login_required
def update_profile():
    """更新当前用户资料"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    try:
        # 处理用户名更新（需要特殊验证）
        if 'username' in data:
            new_username = data['username'].strip()

            # 验证用户名格式
            username_validation = validate_username_server(new_username)
            if not username_validation['valid']:
                return jsonify({
                    "success": False,
                    "message": username_validation['message'],
                    "data": None
                }), 400

            # 检查用户名是否已被其他用户占用
            existing_user = User.query.filter_by(username=new_username).first()
            if existing_user and existing_user.id != current_user.id:
                return jsonify({
                    "success": False,
                    "message": "用户名已被占用",
                    "data": None
                }), 400

            # 更新用户名
            current_user.username = new_username

        # 可更新的其他字段（手机号不允许更新）
        # 由于我们已经简化了用户模型，这里暂时没有其他可更新的字段
        # allowed_fields = []

        # 更新用户资料
        # for field in allowed_fields:
        #     if field in data:
        #         setattr(current_user, field, data[field])

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "资料更新成功",
            "data": current_user.to_dict(include_sensitive=True)
        })

    except Exception as e:
        logger.error(f"更新资料异常: {e}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": "更新失败，请稍后再试",
            "data": None
        }), 500

@auth_api.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    current_password = data.get('current_password', '')
    new_password = data.get('new_password', '')

    # 参数验证
    if not current_password or not new_password:
        return jsonify({
            "success": False,
            "message": "当前密码和新密码不能为空",
            "data": None
        }), 400

    # 验证当前密码
    if not current_user.verify_password(current_password):
        return jsonify({
            "success": False,
            "message": "当前密码错误",
            "data": None
        }), 400

    try:
        # 更新密码
        current_user.password = new_password
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "密码修改成功",
            "data": None
        })
    except Exception as e:
        logger.error(f"修改密码异常: {e}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": "修改失败，请稍后再试",
            "data": None
        }), 500

@auth_api.route('/send-register-sms', methods=['POST'])
def send_register_sms():
    """发送注册短信验证码"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    phone = data.get('phone', '').strip()

    # 参数验证
    if not phone:
        return jsonify({
            "success": False,
            "message": "手机号不能为空",
            "data": None
        }), 400

    # 验证手机号格式
    phone_validation = validate_phone_server(phone)
    if not phone_validation['valid']:
        return jsonify({
            "success": False,
            "message": phone_validation['message'],
            "data": None
        }), 400

    # 检查手机号是否已注册
    user = User.query.filter_by(phone=phone).first()
    if user:
        return jsonify({
            "success": False,
            "message": "该手机号已被注册",
            "data": None
        }), 400

    try:
        # 生成6位验证码
        import random
        sms_code = str(random.randint(100000, 999999))

        # 存储验证码到session
        from flask import session
        session[f'register_sms_code_{phone}'] = sms_code
        session[f'register_sms_time_{phone}'] = __import__('time').time()

        # 这里应该调用短信服务商API发送验证码
        # 开发环境：显示验证码供测试使用
        logger.info(f"注册验证码发送到 {phone}: {sms_code}")

        # 检查是否为开发环境
        import os
        is_development = os.getenv('FLASK_ENV') == 'development' or os.getenv('DEBUG') == 'True'

        if is_development:
            return jsonify({
                "success": True,
                "message": f"验证码已发送（开发环境）: {sms_code}",
                "data": {"dev_code": sms_code}
            })
        else:
            # 生产环境：调用真实短信API
            # TODO: 集成短信服务商API
            return jsonify({
                "success": True,
                "message": "验证码已发送，请查收短信",
                "data": None
            })

    except Exception as e:
        logger.error(f"发送注册验证码异常: {e}")
        return jsonify({
            "success": False,
            "message": "发送失败，请稍后再试",
            "data": None
        }), 500

@auth_api.route('/send-sms', methods=['POST'])
def send_sms():
    """发送登录短信验证码"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    phone = data.get('phone', '').strip()

    # 参数验证
    if not phone:
        return jsonify({
            "success": False,
            "message": "手机号不能为空",
            "data": None
        }), 400

    # 验证手机号格式
    import re
    if not re.match(r'^1[3-9]\d{9}$', phone):
        return jsonify({
            "success": False,
            "message": "请输入有效的手机号码",
            "data": None
        }), 400

    # 检查手机号是否已注册
    user = User.query.filter_by(phone=phone).first()
    if not user:
        return jsonify({
            "success": False,
            "message": "该手机号未注册",
            "data": None
        }), 400

    try:
        # 生成6位验证码
        import random
        sms_code = str(random.randint(100000, 999999))

        # 存储验证码到session（实际项目中应该存储到Redis等缓存中）
        from flask import session
        session[f'sms_code_{phone}'] = sms_code
        session[f'sms_time_{phone}'] = __import__('time').time()

        # 这里应该调用短信服务商API发送验证码
        # 为了演示，我们直接返回成功（实际项目中删除下面的日志）
        logger.info(f"验证码发送到 {phone}: {sms_code}")

        return jsonify({
            "success": True,
            "message": "验证码已发送",
            "data": None
        })

    except Exception as e:
        logger.error(f"发送验证码异常: {e}")
        return jsonify({
            "success": False,
            "message": "发送失败，请稍后再试",
            "data": None
        }), 500

@auth_api.route('/sms-login', methods=['POST'])
def sms_login():
    """短信验证码登录"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    phone = data.get('phone', '').strip()
    sms_code = data.get('sms_code', '').strip()

    # 参数验证
    if not phone or not sms_code:
        return jsonify({
            "success": False,
            "message": "手机号和验证码不能为空",
            "data": None
        }), 400

    # 验证手机号格式
    import re
    if not re.match(r'^1[3-9]\d{9}$', phone):
        return jsonify({
            "success": False,
            "message": "请输入有效的手机号码",
            "data": None
        }), 400

    # 验证码长度检查
    if len(sms_code) != 6:
        return jsonify({
            "success": False,
            "message": "请输入6位验证码",
            "data": None
        }), 400

    try:
        # 验证验证码
        from flask import session
        stored_code = session.get(f'sms_code_{phone}')
        stored_time = session.get(f'sms_time_{phone}')

        if not stored_code or not stored_time:
            return jsonify({
                "success": False,
                "message": "验证码已过期，请重新获取",
                "data": None
            }), 400

        # 检查验证码是否过期（5分钟）
        import time
        if time.time() - stored_time > 300:
            return jsonify({
                "success": False,
                "message": "验证码已过期，请重新获取",
                "data": None
            }), 400

        # 验证验证码
        if stored_code != sms_code:
            return jsonify({
                "success": False,
                "message": "验证码错误",
                "data": None
            }), 400

        # 查找用户
        user = User.query.filter_by(phone=phone).first()
        if not user:
            return jsonify({
                "success": False,
                "message": "该手机号未注册",
                "data": None
            }), 400

        # 检查用户状态
        if not user.is_active:
            return jsonify({
                "success": False,
                "message": "账户已被禁用，请联系管理员",
                "data": None
            }), 403

        # 登录用户
        login_user(user, remember=False)

        # 更新登录信息
        user.update_login_info()
        db.session.commit()

        # 清除验证码
        session.pop(f'sms_code_{phone}', None)
        session.pop(f'sms_time_{phone}', None)

        return jsonify({
            "success": True,
            "message": "登录成功",
            "data": {
                "user_id": user.id,
                "username": user.username,
                "nickname": user.username,  # 昵称，暂时使用用户名
                "role": user.role,
                "role_display": user.get_role_display(),
                "is_admin": user.is_admin,
                "vip_expire_date": user.vip_expire_date.isoformat() if user.vip_expire_date else None,
                "is_pro_valid": user.is_pro_user_valid()
            }
        })

    except Exception as e:
        logger.error(f"验证码登录异常: {e}")
        return jsonify({
            "success": False,
            "message": "登录失败，请稍后再试",
            "data": None
        }), 500

@auth_api.route('/check-username', methods=['POST'])
def check_username():
    """检查用户名是否可用"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "available": False
        }), 400

    username = data.get('username', '').strip()

    if not username:
        return jsonify({
            "success": False,
            "message": "用户名不能为空",
            "available": False
        }), 400

    # 服务端用户名校验
    validation_result = validate_username_server(username)
    if not validation_result['valid']:
        return jsonify({
            "success": False,
            "message": validation_result['message'],
            "available": False
        }), 400

    # 检查用户名是否已存在
    existing_user = User.query.filter_by(username=username).first()
    available = existing_user is None

    return jsonify({
        "success": True,
        "message": "可用" if available else "用户名已被占用",
        "available": available
    })

@auth_api.route('/check-phone', methods=['POST'])
def check_phone():
    """检查手机号是否可用"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "available": False
        }), 400

    phone = data.get('phone', '').strip()

    if not phone:
        return jsonify({
            "success": False,
            "message": "手机号不能为空",
            "available": False
        }), 400

    # 服务端手机号校验
    validation_result = validate_phone_server(phone)
    if not validation_result['valid']:
        return jsonify({
            "success": False,
            "message": validation_result['message'],
            "available": False
        }), 400

    # 检查手机号是否已存在
    existing_user = User.query.filter_by(phone=phone).first()
    available = existing_user is None

    return jsonify({
        "success": True,
        "message": "可用" if available else "手机号已被注册",
        "available": available
    })

@auth_api.route('/check-auth')
def check_auth():
    """检查当前用户认证状态"""
    if current_user.is_authenticated:
        return jsonify({
            "success": True,
            "message": "已登录",
            "data": {
                "user_id": current_user.id,
                "username": current_user.username,
                "nickname": current_user.username,  # 昵称，暂时使用用户名
                "role": current_user.role,
                "role_display": current_user.get_role_display(),
                "is_admin": current_user.is_admin,
                "vip_expire_date": current_user.vip_expire_date.isoformat() if current_user.vip_expire_date else None,
                "is_pro_valid": current_user.is_pro_user_valid()
            }
        })
    else:
        return jsonify({
            "success": False,
            "message": "未登录",
            "data": None
        }), 401

def validate_username_server(username):
    """服务端用户名校验"""
    import re

    # 敏感词列表
    sensitive_words = [
        'admin', 'administrator', 'root', 'system', 'test', 'guest', 'user',
        'null', 'undefined', 'delete', 'drop', 'select', 'insert', 'update',
        '管理员', '系统', '测试', '客服', '官方', '超级', '特权'
    ]

    # 长度检查
    if len(username) < 4 or len(username) > 20:
        return {'valid': False, 'message': '用户名长度必须在4-20个字符之间'}

    # 格式检查
    if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', username):
        return {'valid': False, 'message': '用户名必须以字母开头，只能包含字母、数字和下划线'}

    # 敏感词检查
    username_lower = username.lower()
    for word in sensitive_words:
        if word.lower() in username_lower:
            return {'valid': False, 'message': '用户名包含敏感词，请重新输入'}

    return {'valid': True, 'message': '用户名格式正确'}

def validate_phone_server(phone):
    """服务端手机号校验"""
    import re

    # 基本格式检查
    if not re.match(r'^1[3-9]\d{9}$', phone):
        return {'valid': False, 'message': '请输入有效的中国大陆手机号'}

    # 号段检查
    valid_prefixes = [
        '134', '135', '136', '137', '138', '139', '147', '150', '151', '152',
        '157', '158', '159', '172', '178', '182', '183', '184', '187', '188', '198',  # 移动
        '130', '131', '132', '145', '155', '156', '166', '171', '175', '176', '185', '186',  # 联通
        '133', '149', '153', '173', '177', '180', '181', '189', '199',  # 电信
        '192'  # 广电
    ]

    prefix = phone[:3]
    if prefix not in valid_prefixes:
        return {'valid': False, 'message': '手机号段不正确'}

    return {'valid': True, 'message': '手机号格式正确'}

def validate_password_server(password):
    """服务端密码校验（简化版）"""

    # 基本长度检查
    if len(password) < 6:
        return {'valid': False, 'message': '密码长度不能少于6个字符'}

    if len(password) > 128:
        return {'valid': False, 'message': '密码长度不能超过128个字符'}

    # 只检查是否为空白字符
    if password.strip() != password:
        return {'valid': False, 'message': '密码不能包含前后空格'}

    return {'valid': True, 'message': '密码符合要求'}
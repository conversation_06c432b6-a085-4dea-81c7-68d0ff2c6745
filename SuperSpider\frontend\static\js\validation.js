/**
 * 用户输入校验工具
 * 提供用户名、手机号等字段的实时校验功能
 */

// 敏感词列表
const SENSITIVE_WORDS = [
    'admin', 'administrator', 'root', 'system', 'test', 'guest', 'user',
    'null', 'undefined', 'delete', 'drop', 'select', 'insert', 'update',
    '管理员', '系统', '测试', '客服', '官方', '超级', '特权'
];

// 中国手机号运营商号段
const MOBILE_PREFIXES = {
    '中国移动': ['134', '135', '136', '137', '138', '139', '147', '150', '151', '152', '157', '158', '159', '172', '178', '182', '183', '184', '187', '188', '198'],
    '中国联通': ['130', '131', '132', '145', '155', '156', '166', '171', '175', '176', '185', '186'],
    '中国电信': ['133', '149', '153', '173', '177', '180', '181', '189', '199'],
    '中国广电': ['192']
};

/**
 * 用户名校验
 */
class UsernameValidator {
    constructor() {
        this.rules = {
            length: { min: 4, max: 20 },
            pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
            sensitive: SENSITIVE_WORDS
        };
    }

    /**
     * 校验用户名
     * @param {string} username 用户名
     * @returns {Object} 校验结果
     */
    validate(username) {
        const result = {
            valid: true,
            errors: [],
            warnings: [],
            suggestions: []
        };

        // 长度检查
        if (!username || username.length < this.rules.length.min) {
            result.valid = false;
            result.errors.push(`用户名长度不能少于${this.rules.length.min}个字符`);
        } else if (username.length > this.rules.length.max) {
            result.valid = false;
            result.errors.push(`用户名长度不能超过${this.rules.length.max}个字符`);
        }

        // 格式检查
        if (username && !this.rules.pattern.test(username)) {
            result.valid = false;
            if (/^\d/.test(username)) {
                result.errors.push('用户名不能以数字开头');
            } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                result.errors.push('用户名只能包含字母、数字和下划线');
            } else {
                result.errors.push('用户名格式不正确');
            }
        }

        // 敏感词检查
        if (username) {
            const lowerUsername = username.toLowerCase();
            for (const word of this.rules.sensitive) {
                if (lowerUsername.includes(word.toLowerCase())) {
                    result.valid = false;
                    result.errors.push('用户名包含敏感词，请重新输入');
                    break;
                }
            }
        }

        // 建议
        if (result.valid && username) {
            if (username.length < 6) {
                result.warnings.push('建议用户名长度至少6个字符，更安全');
            }
            if (!/\d/.test(username)) {
                result.suggestions.push('建议在用户名中包含数字，增加唯一性');
            }
        }

        return result;
    }

    /**
     * 生成用户名建议
     * @param {string} base 基础用户名
     * @returns {Array} 建议列表
     */
    generateSuggestions(base) {
        const suggestions = [];
        const cleanBase = base.replace(/[^a-zA-Z0-9]/g, '').substring(0, 15);

        if (cleanBase.length >= 3) {
            suggestions.push(cleanBase + Math.floor(Math.random() * 1000));
            suggestions.push(cleanBase + '_' + Math.floor(Math.random() * 100));
            suggestions.push('user_' + cleanBase);
        }

        return suggestions;
    }
}

/**
 * 手机号校验
 */
class PhoneValidator {
    constructor() {
        this.allPrefixes = [];
        Object.values(MOBILE_PREFIXES).forEach(prefixes => {
            this.allPrefixes.push(...prefixes);
        });
    }

    /**
     * 校验手机号
     * @param {string} phone 手机号
     * @returns {Object} 校验结果
     */
    validate(phone) {
        const result = {
            valid: true,
            errors: [],
            warnings: [],
            info: {}
        };

        // 基本格式检查
        if (!phone) {
            result.valid = false;
            result.errors.push('手机号不能为空');
            return result;
        }

        // 长度检查
        if (phone.length !== 11) {
            result.valid = false;
            result.errors.push('手机号必须是11位数字');
            return result;
        }

        // 数字检查
        if (!/^\d{11}$/.test(phone)) {
            result.valid = false;
            result.errors.push('手机号只能包含数字');
            return result;
        }

        // 首位检查
        if (!phone.startsWith('1')) {
            result.valid = false;
            result.errors.push('手机号必须以1开头');
            return result;
        }

        // 号段检查
        const prefix = phone.substring(0, 3);
        if (!this.allPrefixes.includes(prefix)) {
            result.valid = false;
            result.errors.push('手机号段不正确');
            return result;
        }

        // 获取运营商信息
        result.info.carrier = this.getCarrier(prefix);
        result.info.prefix = prefix;

        // 特殊号码检查
        if (this.isSpecialNumber(phone)) {
            result.warnings.push('检测到特殊号码，请确认是否正确');
        }

        return result;
    }

    /**
     * 获取运营商信息
     * @param {string} prefix 号段前缀
     * @returns {string} 运营商名称
     */
    getCarrier(prefix) {
        for (const [carrier, prefixes] of Object.entries(MOBILE_PREFIXES)) {
            if (prefixes.includes(prefix)) {
                return carrier;
            }
        }
        return '未知运营商';
    }

    /**
     * 检查是否为特殊号码
     * @param {string} phone 手机号
     * @returns {boolean} 是否为特殊号码
     */
    isSpecialNumber(phone) {
        // 连续相同数字
        if (/(\d)\1{6,}/.test(phone)) return true;

        // 连续递增或递减
        let increasing = 0, decreasing = 0;
        for (let i = 1; i < phone.length; i++) {
            const diff = parseInt(phone[i]) - parseInt(phone[i-1]);
            if (diff === 1) increasing++;
            else if (diff === -1) decreasing++;
        }
        if (increasing >= 6 || decreasing >= 6) return true;

        return false;
    }
}

/**
 * 实时校验管理器
 */
class ValidationManager {
    constructor() {
        this.usernameValidator = new UsernameValidator();
        this.phoneValidator = new PhoneValidator();
        this.debounceTimers = {};
    }

    /**
     * 初始化校验
     */
    init() {
        this.bindUsernameValidation();
        this.bindPhoneValidation();
    }

    /**
     * 绑定用户名校验
     */
    bindUsernameValidation() {
        const usernameInput = document.getElementById('register-username');
        const validationDiv = document.getElementById('username-validation');

        if (usernameInput && validationDiv) {
            usernameInput.addEventListener('input', (e) => {
                this.debounceValidation('username', () => {
                    const username = e.target.value.trim();
                    // 当用户名长度>=4时，进行实时唯一性检测
                    const shouldCheckUnique = username.length >= 4;
                    this.validateUsername(username, usernameInput, validationDiv, shouldCheckUnique);
                }, 800); // 800ms防抖，避免频繁请求
            });

            usernameInput.addEventListener('blur', (e) => {
                // 失去焦点时强制检查唯一性
                this.validateUsername(e.target.value.trim(), usernameInput, validationDiv, true);
            });
        }
    }

    /**
     * 绑定手机号校验
     */
    bindPhoneValidation() {
        const phoneInput = document.getElementById('register-phone');
        const validationDiv = document.getElementById('phone-validation');
        const sendSmsBtn = document.getElementById('register-send-sms-btn');

        if (phoneInput && validationDiv) {
            phoneInput.addEventListener('input', (e) => {
                this.debounceValidation('phone', () => {
                    const phone = e.target.value.trim();
                    // 当手机号格式正确时，进行实时唯一性检测
                    const shouldCheckUnique = phone.length === 11 && /^1[3-9]\d{9}$/.test(phone);
                    this.validatePhone(phone, phoneInput, validationDiv, shouldCheckUnique);
                    this.updateSmsButtonState(phone, sendSmsBtn);
                }, 600); // 600ms防抖
            });

            phoneInput.addEventListener('blur', (e) => {
                // 失去焦点时强制检查唯一性
                this.validatePhone(e.target.value.trim(), phoneInput, validationDiv, true);
            });
        }

        // 绑定发送验证码按钮
        if (sendSmsBtn) {
            sendSmsBtn.addEventListener('click', () => {
                this.sendRegisterSms();
            });
        }

        // 绑定验证码输入框
        const smsCodeInput = document.getElementById('register-sms-code');
        const smsValidationDiv = document.getElementById('register-sms-validation');

        if (smsCodeInput && smsValidationDiv) {
            smsCodeInput.addEventListener('input', (e) => {
                this.validateSmsCode(e.target.value, smsCodeInput, smsValidationDiv);
            });
        }
    }

    /**
     * 防抖校验
     */
    debounceValidation(key, callback, delay) {
        clearTimeout(this.debounceTimers[key]);
        this.debounceTimers[key] = setTimeout(callback, delay);
    }

    /**
     * 校验用户名
     */
    async validateUsername(username, inputElement, validationElement, checkUnique = false) {
        // 先进行基本格式校验
        const result = this.usernameValidator.validate(username);

        // 如果基本格式校验通过，且需要检查唯一性
        if (result.valid && checkUnique && username.length >= 4) {
            inputElement.classList.add('checking');
            this.showValidationMessage(validationElement, '正在检查用户名是否可用...', 'info');

            try {
                const response = await fetch('/api/auth/check-username', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username })
                });

                const data = await response.json();
                inputElement.classList.remove('checking');

                if (data.success && !data.available) {
                    result.valid = false;
                    result.errors = ['用户名已被占用']; // 替换错误信息
                } else if (data.success && data.available) {
                    // 用户名可用，添加成功标记
                    result.successMessage = '✓ 用户名可用';
                }
            } catch (error) {
                console.error('检查用户名唯一性失败:', error);
                inputElement.classList.remove('checking');
                result.warnings.push('无法验证用户名唯一性，请稍后重试');
            }
        }

        this.updateValidationUI(inputElement, validationElement, result);
    }

    /**
     * 校验手机号
     */
    async validatePhone(phone, inputElement, validationElement, checkUnique = false) {
        // 先进行基本格式校验
        const result = this.phoneValidator.validate(phone);

        // 如果基本格式校验通过，且需要检查唯一性
        if (result.valid && checkUnique) {
            inputElement.classList.add('checking');
            this.showValidationMessage(validationElement, '正在检查手机号是否已注册...', 'info');

            try {
                const response = await fetch('/api/auth/check-phone', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ phone })
                });

                const data = await response.json();
                inputElement.classList.remove('checking');

                if (data.success && !data.available) {
                    result.valid = false;
                    result.errors = ['手机号已被注册']; // 替换错误信息
                } else if (data.success && data.available) {
                    // 手机号可用，添加成功标记
                    const carrier = result.info?.carrier || '';
                    result.successMessage = `✓ 手机号可用${carrier ? ` (${carrier})` : ''}`;
                }
            } catch (error) {
                console.error('检查手机号唯一性失败:', error);
                inputElement.classList.remove('checking');
                result.warnings.push('无法验证手机号唯一性，请稍后重试');
            }
        }

        this.updateValidationUI(inputElement, validationElement, result);
    }

    /**
     * 更新校验UI
     */
    updateValidationUI(inputElement, validationElement, result) {
        // 更新输入框样式
        inputElement.classList.remove('valid', 'invalid', 'checking');

        if (result.valid) {
            inputElement.classList.add('valid');
        } else {
            inputElement.classList.add('invalid');
        }

        // 显示校验消息
        if (result.errors.length > 0) {
            this.showValidationMessage(validationElement, result.errors[0], 'error');
        } else if (result.warnings.length > 0) {
            this.showValidationMessage(validationElement, result.warnings[0], 'warning');
        } else if (result.valid) {
            // 优先显示自定义成功消息，否则显示默认消息
            const message = result.successMessage || '✓ 格式正确';
            this.showValidationMessage(validationElement, message, 'success');
        } else {
            this.hideValidationMessage(validationElement);
        }
    }

    /**
     * 显示校验消息
     */
    showValidationMessage(element, message, type) {
        element.textContent = message;
        element.className = `validation-message ${type}`;
        element.style.display = 'block';
    }

    /**
     * 隐藏校验消息
     */
    hideValidationMessage(element) {
        element.style.display = 'none';
    }

    /**
     * 更新发送验证码按钮状态
     */
    updateSmsButtonState(phone, button) {
        if (!button) return;

        const result = this.phoneValidator.validate(phone);
        if (result.valid) {
            button.disabled = false;
            button.textContent = '发送验证码';
        } else {
            button.disabled = true;
            button.textContent = '发送验证码';
        }
    }

    /**
     * 发送注册验证码
     */
    async sendRegisterSms() {
        const phoneInput = document.getElementById('register-phone');
        const sendSmsBtn = document.getElementById('register-send-sms-btn');
        const validationDiv = document.getElementById('register-sms-validation');

        if (!phoneInput || !sendSmsBtn) return;

        const phone = phoneInput.value.trim();

        // 验证手机号
        const result = this.phoneValidator.validate(phone);
        if (!result.valid) {
            this.showValidationMessage(validationDiv, '请先输入正确的手机号', 'error');
            return;
        }

        try {
            // 禁用按钮
            sendSmsBtn.disabled = true;
            sendSmsBtn.textContent = '发送中...';

            const response = await fetch('/api/auth/send-register-sms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    phone: phone
                })
            });

            const data = await response.json();

            if (data.success) {
                // 检查是否为开发环境并显示验证码
                if (data.data && data.data.dev_code) {
                    this.showValidationMessage(validationDiv, data.message, 'success');
                    // 自动填充验证码（开发环境）
                    const smsCodeInput = document.getElementById('register-sms-code');
                    if (smsCodeInput) {
                        smsCodeInput.value = data.data.dev_code;
                        smsCodeInput.style.backgroundColor = '#e8f5e8';
                    }
                } else {
                    this.showValidationMessage(validationDiv, '验证码已发送，请查收', 'success');
                }

                // 开始倒计时
                this.startSmsCountdown(sendSmsBtn);
            } else {
                this.showValidationMessage(validationDiv, data.message || '发送失败', 'error');
                // 恢复按钮
                sendSmsBtn.disabled = false;
                sendSmsBtn.textContent = '发送验证码';
            }
        } catch (error) {
            console.error('发送验证码请求失败:', error);
            this.showValidationMessage(validationDiv, '发送失败，请稍后再试', 'error');
            // 恢复按钮
            sendSmsBtn.disabled = false;
            sendSmsBtn.textContent = '发送验证码';
        }
    }

    /**
     * 验证码倒计时
     */
    startSmsCountdown(button) {
        let countdown = 60;
        button.classList.add('countdown');

        const timer = setInterval(() => {
            button.textContent = `${countdown}秒后重发`;
            countdown--;

            if (countdown < 0) {
                clearInterval(timer);
                button.disabled = false;
                button.classList.remove('countdown');
                button.textContent = '发送验证码';
            }
        }, 1000);
    }

    /**
     * 校验验证码
     */
    validateSmsCode(code, inputElement, validationElement) {
        if (!code) {
            this.hideValidationMessage(validationElement);
            inputElement.classList.remove('valid', 'invalid');
            return;
        }

        if (code.length !== 6 || !/^\d{6}$/.test(code)) {
            inputElement.classList.remove('valid');
            inputElement.classList.add('invalid');
            this.showValidationMessage(validationElement, '请输入6位数字验证码', 'error');
        } else {
            inputElement.classList.remove('invalid');
            inputElement.classList.add('valid');
            this.showValidationMessage(validationElement, '✓ 格式正确', 'success');
        }
    }
}

// 全局实例
const validationManager = new ValidationManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    validationManager.init();
});

// 导出供其他模块使用
window.ValidationManager = ValidationManager;
window.UsernameValidator = UsernameValidator;
window.PhoneValidator = PhoneValidator;

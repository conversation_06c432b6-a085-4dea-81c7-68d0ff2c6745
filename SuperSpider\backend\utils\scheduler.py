#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
定时任务调度器
处理系统定期任务，如清理过期用户等
"""

import logging
import threading
import time
from datetime import datetime, timedelta

from backend.utils.permissions import PermissionManager

# 创建日志记录器
logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器"""

    def __init__(self):
        self.running = False
        self.thread = None
        self.tasks = []

    def add_task(self, func, interval_hours=24, name="Unknown Task"):
        """
        添加定时任务

        Args:
            func: 要执行的函数
            interval_hours: 执行间隔（小时）
            name: 任务名称
        """
        task = {
            'func': func,
            'interval': timedelta(hours=interval_hours),
            'name': name,
            'last_run': None,
            'next_run': datetime.now()
        }
        self.tasks.append(task)
        logger.info(f"添加定时任务: {name}, 间隔: {interval_hours}小时")

    def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已在运行")
            return

        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        logger.info("任务调度器已启动")

    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("任务调度器已停止")

    def _run_scheduler(self):
        """调度器主循环"""
        while self.running:
            try:
                current_time = datetime.now()

                for task in self.tasks:
                    if current_time >= task['next_run']:
                        try:
                            logger.info(f"执行定时任务: {task['name']}")
                            result = task['func']()
                            task['last_run'] = current_time
                            task['next_run'] = current_time + task['interval']
                            logger.info(f"任务 {task['name']} 执行完成，结果: {result}")
                        except Exception as e:
                            logger.error(f"任务 {task['name']} 执行失败: {e}")
                            # 即使失败也要更新下次执行时间，避免一直重试
                            task['next_run'] = current_time + task['interval']

                # 每分钟检查一次
                time.sleep(60)

            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(60)

# 全局调度器实例
scheduler = TaskScheduler()

def cleanup_expired_users_task():
    """清理过期用户的定时任务"""
    try:
        from backend.main import app
        with app.app_context():
            downgraded_count = PermissionManager.check_and_downgrade_expired_users()
            return f"降级了 {downgraded_count} 个过期用户"
    except Exception as e:
        logger.error(f"清理过期用户任务失败: {e}")
        return f"任务失败: {e}"

def daily_reset_task():
    """每日重置任务（凌晨12点执行）"""
    try:
        from backend.main import app, db
        from backend.models.user_usage import UserUsage
        from backend.models.vip_account import VipAccount
        import datetime

        with app.app_context():
            reset_count = 0

            # 重置所有用户的每日使用统计
            today = datetime.date.today()
            yesterday = today - datetime.timedelta(days=1)

            # 为有昨天记录的用户创建今天的记录
            yesterday_records = UserUsage.query.filter_by(date=yesterday).all()

            for record in yesterday_records:
                # 检查今天是否已有记录
                today_record = UserUsage.query.filter_by(
                    user_id=record.user_id,
                    date=today
                ).first()

                if not today_record:
                    # 创建今天的新记录，重置计数
                    new_record = UserUsage(
                        user_id=record.user_id,
                        date=today,
                        download_count=0,
                        api_call_count=0,
                        api_calls_this_minute=0
                    )
                    db.session.add(new_record)
                    reset_count += 1

            # 重置VIP账号的每日使用次数
            vip_accounts = VipAccount.query.all()
            vip_reset_count = 0

            for account in vip_accounts:
                if account.daily_uses > 0:
                    account.daily_uses = 0
                    account.last_reset_date = today
                    vip_reset_count += 1

            db.session.commit()

            return f"每日重置完成: 用户统计 {reset_count} 个，VIP账号 {vip_reset_count} 个"

    except Exception as e:
        logger.error(f"每日重置任务失败: {e}")
        return f"任务失败: {e}"

def init_scheduler():
    """初始化调度器和任务"""
    # 添加清理过期用户任务，每6小时执行一次
    scheduler.add_task(
        func=cleanup_expired_users_task,
        interval_hours=6,
        name="清理过期Pro用户"
    )

    # 添加每日重置任务，每24小时执行一次（凌晨12点）
    scheduler.add_task(
        func=daily_reset_task,
        interval_hours=24,
        name="每日重置用户统计"
    )

    # 启动调度器
    scheduler.start()
    logger.info("定时任务调度器初始化完成")

def shutdown_scheduler():
    """关闭调度器"""
    scheduler.stop()
    logger.info("定时任务调度器已关闭")

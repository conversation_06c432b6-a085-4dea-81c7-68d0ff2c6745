#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
错误处理工具模块
提供统一的错误处理和用户友好的错误信息转换
"""

import logging
from typing import Dict, Any
from requests.exceptions import ConnectionError, Timeout, RequestException
from urllib3.exceptions import NameResolutionError

logger = logging.getLogger(__name__)


class ErrorHandler:
    """错误处理工具类"""
    
    @staticmethod
    def get_user_friendly_error(error: Exception, platform: str = "") -> str:
        """
        将技术错误转换为用户友好的错误信息
        
        Args:
            error: 异常对象
            platform: 平台名称（如：快手、抖音等）
            
        Returns:
            str: 用户友好的错误信息
        """
        error_str = str(error).lower()
        platform_text = f"{platform}" if platform else "视频"
        
        # 网络连接相关错误
        if isinstance(error, (ConnectionError, NameResolutionError)) or 'failed to resolve' in error_str:
            return "网络连接失败，请检查网络连接或稍后重试"
        
        # 超时错误
        elif isinstance(error, Timeout) or 'timeout' in error_str:
            return "请求超时，请检查网络连接或稍后重试"
        
        # 重试次数超限
        elif 'max retries exceeded' in error_str:
            return "网络请求失败，服务器可能暂时不可用，请稍后重试"
        
        # SSL/证书错误
        elif 'ssl' in error_str or 'certificate' in error_str:
            return "安全连接失败，请检查网络设置"
        
        # 权限错误
        elif 'permission denied' in error_str or '403' in error_str:
            return "访问被拒绝，可能是网络限制或防火墙阻止"
        
        # URL格式错误
        elif 'invalid url' in error_str or 'malformed url' in error_str:
            return f"{platform_text}链接格式不正确，请检查链接是否完整"
        
        # HTTP状态码错误
        elif '404' in error_str:
            return f"{platform_text}不存在或已被删除"
        elif '500' in error_str:
            return "服务器内部错误，请稍后重试"
        elif '502' in error_str or '503' in error_str:
            return "服务器暂时不可用，请稍后重试"
        
        # 浏览器驱动相关错误
        elif 'drissionpage' in error_str or 'chrome' in error_str or 'webdriver' in error_str:
            return "浏览器驱动异常，请稍后重试"
        
        # JSON解析错误
        elif 'json' in error_str and 'decode' in error_str:
            return f"{platform_text}数据格式异常，无法解析"
        
        # 视频相关特定错误
        elif '视频' in error_str:
            return str(error)  # 已经是中文错误信息，直接返回
        
        # 默认错误
        else:
            return f"解析失败，请检查{platform_text}链接是否有效或稍后重试"
    
    @staticmethod
    def create_error_response(message: str, data: Any = None) -> Dict[str, Any]:
        """
        创建标准的错误响应格式
        
        Args:
            message: 错误信息
            data: 附加数据
            
        Returns:
            Dict: 标准错误响应
        """
        return {
            "success": False,
            "message": message,
            "data": data
        }
    
    @staticmethod
    def create_success_response(message: str, data: Any = None) -> Dict[str, Any]:
        """
        创建标准的成功响应格式
        
        Args:
            message: 成功信息
            data: 响应数据
            
        Returns:
            Dict: 标准成功响应
        """
        return {
            "success": True,
            "message": message,
            "data": data
        }
    
    @staticmethod
    def handle_spider_error(error: Exception, platform: str = "", logger_instance: logging.Logger = None) -> Dict[str, Any]:
        """
        处理爬虫错误的统一方法
        
        Args:
            error: 异常对象
            platform: 平台名称
            logger_instance: 日志记录器实例
            
        Returns:
            Dict: 错误响应
        """
        if logger_instance:
            logger_instance.error(f"{platform}爬虫执行失败: {str(error)}")
        else:
            logger.error(f"{platform}爬虫执行失败: {str(error)}")
        
        # 检查是否已经是用户友好的错误信息
        user_error = str(error) if any(keyword in str(error) for keyword in 
                                     ['网络连接失败', '请求超时', '视频', '链接', '格式', '浏览器', '服务器']) else ErrorHandler.get_user_friendly_error(error, platform)
        
        return ErrorHandler.create_error_response(user_error)


# 常用的错误信息常量
class ErrorMessages:
    """常用错误信息常量"""
    
    # 参数错误
    MISSING_URL = "请提供视频链接"
    INVALID_URL = "请提供有效的视频链接"
    
    # 解析错误
    PARSE_FAILED = "无法解析视频信息，请检查链接是否有效"
    VIDEO_NOT_FOUND = "视频不存在或已被删除"
    VIDEO_PRIVATE = "视频信息不完整，可能是私密视频或已被删除"
    
    # 网络错误
    NETWORK_ERROR = "网络连接失败，请检查网络连接或稍后重试"
    TIMEOUT_ERROR = "请求超时，请检查网络连接或稍后重试"
    SERVER_ERROR = "服务器暂时不可用，请稍后重试"
    
    # 成功信息
    PARSE_SUCCESS = "解析成功"
    DOWNLOAD_SUCCESS = "下载成功"
